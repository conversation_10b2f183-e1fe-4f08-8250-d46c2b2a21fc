import datetime
from typing import Any

import sqlalchemy
from sqlalchemy import func, desc, exc
from sqlalchemy.exc import NoResultFound, IntegrityError

from core.db.mysql_cn import DBSession, WorkAccount, CollectionUser, WorkAccountJoinGroup, TargetAccountSportYf3, \
    WorkAccountStatusAddContact, Contact, PullGroup, PlanCheckGroup, TdataBak, PlanCheckGroupRes, CollectionGroup, \
    CollectionUserGroup, SimilarChannel, SponsoredInfo, SponsoredRelationship, ForwardUser, WorkAccountStatusPullGroup, \
    InitCreateGroup, SystemConf, SearchChannelTask, SearchGroup

import pymysql

pymysql.install_as_MySQLdb()


class MySqlAccountFun(DBSession):
    # work account ---------------------------------------------------------------------------

    def work_account_insert_account_info(self, work_phone, proxy_port, source, country, create_time, reg_time):
        """插入工作账号信息"""
        with self.Session() as session:
            data = {
                "work_phone": work_phone,
                "country": country,
                "proxy_port": proxy_port,
                "can_use_time": datetime.datetime.now(),
                "reg_time": reg_time,
                "next_can_find_time": datetime.datetime.now(),
                "next_can_raise_time": datetime.datetime.now(),
                "next_can_send_time": datetime.datetime.now(),
                "create_time": create_time,
                "source": source
            }
            add_info = WorkAccount(**data)
            session.add(add_info)
            session.commit()

    def update_work_account_and_proxy_info(self, work_phone, proxy_hash):
        """如果work_phone没存在与WorkAccount表中则新增,如果存在则更新proxy_hash"""
        with self.Session() as session:
            # 查询是否存在对应的 work_phone
            work_account = session.query(WorkAccount).filter_by(work_phone=work_phone).first()

            if work_account:
                # 如果存在，更新 proxy_hash
                work_account.proxy_hash = proxy_hash
                session.commit()
                print(f"Updated proxy_hash for work_phone: {work_phone}")
            else:
                # 如果不存在，新增一条记录
                new_work_account = WorkAccount(work_phone=work_phone,
                                               proxy_hash=proxy_hash,
                                               save_status=1,
                                               remark='cs_used',
                                               create_time=datetime.datetime.now(),
                                               next_can_raise_time=datetime.datetime.now(),
                                               next_can_send_time=datetime.datetime.now(),
                                               next_can_find_time=datetime.datetime.now())
                session.add(new_work_account)
                session.commit()
                print(f"Added new work_account with work_phone: {work_phone}")

    def work_account_update_proxy_hash(self, work_phone, proxy_hash, proxy_host, proxy_port):
        with self.Session() as session:
            session.query(WorkAccount).filter(WorkAccount.work_phone == work_phone).update(
                {WorkAccount.proxy_hash: proxy_hash,
                 WorkAccount.proxy_host: proxy_host,
                 WorkAccount.proxy_port: proxy_port})
            session.commit()

    def work_account_get_used_proxy_hash(self, used_count) -> list[Any] | bool:
        """查询出账号表中,存活账号使用的proxy_hash,并且hash使用次数达到4次以上的hash"""
        with self.Session() as session:
            proxy_hash_list = []
            proxy_hash_count: list[tuple] = session.query(WorkAccount.proxy_hash).filter(
                WorkAccount.save_status == 1,
                WorkAccount.proxy_hash != None
            ).group_by(WorkAccount.proxy_hash).having(func.count(WorkAccount.proxy_hash) >= used_count).all()
            if proxy_hash_count:
                for proxy_hash in proxy_hash_count:
                    proxy_hash_list.append(proxy_hash[0])
                return proxy_hash_list
            else:
                return False

    def work_account_get_no_used_proxy_hash(self, used_count) -> list[Any] | bool:
        """查询出账号表中,存活账号使用的proxy_hash,并且hash使用次数达到4次以上的hash"""
        with self.Session() as session:
            proxy_hash_list = []
            proxy_hash_count: list[tuple] = session.query(WorkAccount.proxy_hash).filter(
                WorkAccount.save_status == 1,
                WorkAccount.proxy_hash != None
            ).group_by(WorkAccount.proxy_hash).having(func.count(WorkAccount.proxy_hash) <= used_count).all()
            if proxy_hash_count:
                for proxy_hash in proxy_hash_count:
                    proxy_hash_list.append(proxy_hash[0])
                return proxy_hash_list
            else:
                return False

    def get_work_accounts(self):
        with self.Session() as session:
            work_accounts = session.query(WorkAccount).filter(WorkAccount.save_status == 1,
                                                              WorkAccount.use_status == 0).all()
            if not work_accounts:
                return False
            return work_accounts

    def get_work_accounts_by_account_type(self,account_type):
        with self.Session() as session:
            work_accounts = session.query(WorkAccount).filter(WorkAccount.save_status == 1,
                                                              WorkAccount.use_status == 0,
                                                              WorkAccount.account_type == account_type).all()
            if not work_accounts:
                return False
            return work_accounts

    def work_account_get_one_account(self, work_phone):
        with self.Session() as session:
            work_account = session.query(WorkAccount).filter(
                WorkAccount.work_phone == work_phone,
                WorkAccount.save_status == 1).first()
            if work_account:
                return work_account
            return False

    def get_one_account(self, work_phone):
        with self.Session() as session:
            work_account = session.query(WorkAccount).filter(work_phone == work_phone,
                                                             WorkAccount.use_status == 0,
                                                             WorkAccount.save_status == 1,
                                                             WorkAccount.account_type == 'collection').first()
            return work_account.__dict__ if work_account else None

    def get_collection_collection_accounts(self):
        with self.Session() as session:
            collection_accounts = session.query(WorkAccount).filter(
                WorkAccount.account_type == 'collection',
                WorkAccount.save_status == 1,
                WorkAccount.use_status == 0
            ).all()
            return collection_accounts

    def get_can_use_work_account(self):
        """获取可用账号"""
        with self.Session() as session:
            work_accounts = session.query(
                WorkAccount
            ).filter(
                WorkAccount.use_status == 0,
                WorkAccount.save_status == 1,
            ).offset(4000).all()
            if work_accounts:
                return [account.__dict__ for account in work_accounts]
            return False

    def get_has_no_bak_accounts(self):
        with self.Session() as session:
            work_accounts = session.query(
                WorkAccount
            ).filter(
                WorkAccount.use_status == 0,
                WorkAccount.save_status == 1,
            ).all()
            if work_accounts:
                return [account.__dict__ for account in work_accounts]
            return False

    def work_account_update_over_task_status(self, data: dict):
        """更新工作账号任务结束状态"""
        work_phone = data.get('work_phone')
        over_task_status = data.get('over_task_status')
        with self.Session() as session:
            work_account: WorkAccount = session.query(WorkAccount).filter(
                WorkAccount.work_phone == work_phone).first()
            if work_account:
                work_account.over_task_status = over_task_status,
                session.commit()
            else:
                return False

    def work_account_update_can_use_time(self, data: dict):
        work_phone = data.get('work_phone')
        seconds = data.get('seconds')
        """更新工作账号可用时间"""
        with self.Session() as session:
            work_account: WorkAccount = session.query(WorkAccount).filter(
                WorkAccount.work_phone == work_phone).first()
            if work_account:
                work_account.can_use_time = datetime.datetime.now() + datetime.timedelta(seconds=seconds)
                session.commit()
            else:
                return False

    def work_account_get_proxy_port(self, country_code, used_count):
        """获取一个新的proxy_port，如果没有找到符合条件的proxy_port，返回None"""
        with self.Session() as session:
            # 创建一个子查询，找出proxy_port的总量不超过3个的proxy_port
            subquery = session.query(WorkAccount.proxy_port).group_by(
                WorkAccount.proxy_port
            ).having(func.count(WorkAccount.proxy_port) <= used_count).scalar_subquery()

            # 在主查询中使用子查询，并添加country为指定值的条件
            proxy_port = session.query(WorkAccount.proxy_port).filter(
                WorkAccount.country == country_code,
                WorkAccount.proxy_port.in_(subquery)
            ).order_by(func.rand()).first()

            if proxy_port:
                return proxy_port[0]
            else:
                return None

    def work_account_update_proxy_port(self, data: dict):
        """更新proxy_port"""
        work_phone = data.get('work_phone')
        proxy_port = data.get('proxy_port')
        with self.Session() as session:
            session.query(WorkAccount).filter(WorkAccount.work_phone == work_phone).update(
                {WorkAccount.proxy_port: proxy_port})
            session.commit()

    def work_account_get_used_proxy_port_list(self):
        """获取已经使用的proxy_port"""
        with self.Session() as session:
            proxy_list = []
            proxy_port_list: list[tuple] = session.query(WorkAccount.proxy_port).all()
            if proxy_port_list:
                for proxy_port in proxy_port_list:
                    proxy_list.append(proxy_port[0])
                return list(set(proxy_list))
            else:
                return False

    def work_account_update_use_status(self, data: dict):
        """更新工作账号使用状态"""
        work_phone = data.get('work_phone')
        use_status = data.get('use_status')
        with self.Session() as session:
            work_account: WorkAccount = session.query(WorkAccount).filter(
                WorkAccount.work_phone == work_phone).first()
            if work_account:
                work_account.use_status = use_status,
                session.commit()
            else:
                return False

    def work_account_update_save_status(self, data: dict):
        """更新工作账号存活状态"""
        work_phone = data.get('work_phone')
        save_status = data.get('save_status')
        death_reason = data.get('death_reason')
        with self.Session() as session:
            work_account: WorkAccount = session.query(WorkAccount).filter(
                WorkAccount.work_phone == work_phone).first()
            if work_account:
                work_account.save_status = save_status,
                work_account.death_time = datetime.datetime.now()
                work_account.death_reason = death_reason
                session.commit()
            else:
                return False

    # def work_account_update_is_add_contacts(self, data: dict):
    #     """更新是否添加联系人"""
    #     work_phone = data.get('work_phone')
    #     with self.Session() as session:
    #         session.query(WorkAccount).filter(WorkAccount.work_phone == work_phone).update(
    #             {WorkAccount.is_add_contacts: 1})
    #         session.commit()

    def work_account_update_info(self, data: dict):
        """更新工作账号信息"""
        work_phone = data.get('work_phone')
        info_dict = data.get('info_dict')
        username = info_dict.get('username')
        is_avatar = info_dict.get('is_avatar')
        user_id = info_dict.get('user_id')
        first_name = info_dict.get('first_name')
        last_name = info_dict.get('last_name')
        country = info_dict.get('country')
        is_clear_session = info_dict.get('is_clear_session')

        with self.Session() as session:
            res = session.query(WorkAccount).filter(WorkAccount.work_phone == work_phone).update({
                WorkAccount.username: username,
                WorkAccount.user_id: user_id,
                WorkAccount.first_name: first_name,
                WorkAccount.last_name: last_name,
                WorkAccount.country: country,
                WorkAccount.is_clear_session: is_clear_session,
                WorkAccount.is_avatar: is_avatar})
            print(res)
            session.commit()

    def work_account_check_has_account(self, user_id):
        """检查是否有账号"""
        with self.Session() as session:
            res = session.query(WorkAccount).filter(WorkAccount.user_id == user_id).first()
            if res:
                return True
            else:
                return False

    # work account status add contact ---------------------------------------------------------------------------
    def work_account_status_insert_info(self, data: dict):
        """插入账号状态信息"""
        with self.Session() as session:
            session.add(WorkAccountStatusAddContact(**data))
            session.commit()

    def work_account_status_get_info(self, limit):
        """获取最新的10条数据"""
        with self.Session() as session:
            work_account_status = session.query(WorkAccountStatusAddContact).order_by(
                desc(WorkAccountStatusAddContact.add_contacts_time)).limit(limit).all()
            return work_account_status

    # work account pull group ---------------------------------------------------------------------------
    def work_account_pull_group_insert_info(self, data: dict):
        """插入拉群信息"""
        with self.Session() as session:
            session.add(WorkAccountStatusPullGroup(**data))
            session.commit()

    # add group --------------------------------------------------------------------------------
    def add_group_insert_info(self, data: dict):
        """插入群组信息"""
        work_phone = data.get('work_phone')
        group_username = data.get('group_username')
        try:
            with self.Session() as session:
                data = {"work_phone": work_phone, "group_username": group_username,
                        "join_time": datetime.datetime.now(),
                        "update_time": datetime.datetime.now()}
                session.add(WorkAccountJoinGroup(**data))
                session.commit()
        except Exception as e:
            print(e)
            session.rollback()
            return False

    def add_group_update_join_status(self, data: dict):
        """更新加入状态"""
        work_phone = data.get('work_phone')
        group_username = data.get('group_username')
        join_status = data.get('join_status')
        with self.Session() as session:
            session.query(WorkAccountJoinGroup).filter(WorkAccountJoinGroup.work_phone == work_phone,
                                                       WorkAccountJoinGroup.group_username == group_username).update(
                {WorkAccountJoinGroup.join_status: join_status})
            session.commit()

    def add_group_check_is_join_group(self, group_username):
        with self.Session() as session:
            group: WorkAccountJoinGroup = session.query(WorkAccountJoinGroup).filter(
                WorkAccountJoinGroup.group_username == group_username).first()
            if group:
                return group
            return False

    # collection user --------------------------------------------------------------------------------
    def collection_user_get_forward_msg(self, label):
        with self.Session() as session:
            forward_msg: CollectionUser = session.query(CollectionUser).filter(
                CollectionUser.label == label,
                CollectionUser.forward_group_id == None
            ).with_for_update().first()
            if forward_msg:
                forward_msg.forward_group_id = 1
                session.commit()
                return forward_msg
            return False

    def collection_user_get_forward_msg_by_group_username(self, group_username):
        with self.Session() as session:
            forward_msg: CollectionUser = session.query(CollectionUser).filter(
                CollectionUser.collection_group_username == group_username,
                CollectionUser.forward_group_id == None
            ).with_for_update().first()
            if forward_msg:
                forward_msg.forward_group_id = 1
                session.commit()
                return forward_msg
            return False

    def collection_user_update_forward_user(self, data: dict):
        """更新转发用户"""
        user_id = data.get('user_id')
        with self.Session() as session:
            session.query(CollectionUser).filter(CollectionUser.user_id == user_id).update(
                {
                    CollectionUser.forward_group_id: data.get('forward_group_id'),
                    CollectionUser.forward_group_username: data.get('forward_group_username'),
                    CollectionUser.forward_msg_id: data.get('forward_msg_id'),
                })
            session.commit()

    def collection_user_update_forward_status(self, data: dict):
        user_id = data.get('user_id')
        with self.Session() as session:
            session.query(CollectionUser).filter(CollectionUser.user_id == user_id).update(
                {
                    CollectionUser.forward_group_id: data.get('forward_group_id'),
                })
            session.commit()

    # def update_collection_user_pull_status(self, data: dict):
    #     """更新用户拉取状态"""
    #     user_id = data.get('user_id')
    #     pull_status = data.get('pull_status')
    #     with self.Session() as session:
    #         session.query(CollectionUser).filter(CollectionUser.user_id == user_id).update(
    #             {CollectionUser.pull_status: pull_status})
    #         session.commit()

    # def update_collection_user_premium_permissions(self, data: dict):
    #     """更新用户权限"""
    #     user_id = data.get('user_id')
    #     permissions_invite = data.get('permissions_invite')
    #     permissions_msg = data.get('permissions_msg')
    #     with self.Session() as session:
    #         session.query(CollectionUser).filter(CollectionUser.user_id == user_id).update(
    #             {CollectionUser.premium_would_allow_invite: permissions_invite,
    #              CollectionUser.premium_required_for_pm: permissions_msg})
    #         session.commit()

    # contact --------------------------------------------------------------------------------
    def contact_insert_info(self, data: dict):
        """插入联系人信息"""
        with self.Session() as session:
            try:
                session.add(Contact(**data))
                session.commit()
            except sqlalchemy.exc.IntegrityError:
                print(f'IntegrityError -- {data.get("target_phone")}')
                session.rollback()

    def contact_get_contacts(self, work_phone):
        with self.Session() as session:
            contacts = session.query(Contact).filter(
                Contact.work_phone == work_phone,
                Contact.forward_group_title == None,
                Contact.forward_group_title == None,
                Contact.forward_group_msg_id == None,
            ).first()
            return contacts

    def contact_update_forward_group_info(self, data: dict):
        """更新转发群信息"""
        target_phone = data.get('target_phone')
        forward_group_username = data.get('forward_group_username')
        forward_group_title = data.get('forward_group_title')
        forward_group_msg_id = data.get('forward_group_msg_id')
        with self.Session() as session:
            session.query(Contact).filter(Contact.target_phone == target_phone).update(
                {Contact.forward_group_username: forward_group_username,
                 Contact.forward_group_title: forward_group_title,
                 Contact.forward_group_msg_id: forward_group_msg_id})
            session.commit()

    def contact_get_contacts_by_wor_phone(self, work_phone):
        with self.Session() as session:
            contacts = session.query(Contact).filter(
                Contact.work_phone == work_phone,
            ).all()
            return contacts

    def contact_update_target_user_online_time(self, data: dict):
        """更新目标用户在线时间"""
        user_id = data.get('user_id')
        online_time = data.get('online_time')
        with self.Session() as session:
            session.query(Contact).filter(Contact.user_id == user_id).update(
                {Contact.user_was_online_time: online_time,
                 Contact.check_online_time: datetime.datetime.now()})
            session.commit()

    def contact_get_forward_contacts(self, work_phone, label):
        with self.Session() as session:
            contacts = session.query(Contact).filter(
                Contact.work_phone == work_phone,
                Contact.forward_group_title == None,
                Contact.forward_group_title == None,
                Contact.forward_group_msg_id == None,
                Contact.label == label
            ).all()
            return contacts

    def contact_update_forward_contacts(self, data: dict):
        with self.Session() as session:
            session.query(Contact).filter(Contact.user_id == data.get('user_id'),
                                          Contact.work_phone == data.get('work_phone')).update(
                {Contact.forward_group_title: data.get('forward_group_title'),
                 Contact.forward_group_msg_id: data.get('forward_group_msg_id'),
                 Contact.forward_group_username: data.get('forward_group_username')})
            session.commit()

    def contact_get_work_phone_has_contacts(self, work_phone, label):
        with self.Session() as session:
            contacts = session.query(Contact).filter(
                Contact.work_phone == work_phone,
                Contact.label == label,
                Contact.forward_group_msg_id == None
            ).first()
            if contacts:
                return True
            else:
                return False

    # target phone --------------------------------------------------------------------------------
    def target_phone_update_info(self, data: dict):
        """更新目标电话信息"""
        with self.Session() as session:
            work_phone = data.get('work_phone')
            target_phone = data.get('target_phone')
            is_found = data.get('is_found')
            is_tg_account = data.get('is_tg_account')
            session.query(TargetAccountSportYf3).filter(TargetAccountSportYf3.target_phone == target_phone
                                                        ).update(
                {TargetAccountSportYf3.is_found: is_found,
                 TargetAccountSportYf3.is_tg_account: is_tg_account,
                 TargetAccountSportYf3.work_phone: work_phone,
                 TargetAccountSportYf3.found_time: datetime.datetime.now(),
                 TargetAccountSportYf3.update_time: datetime.datetime.now()})

            session.commit()

    def target_phone_get_phone(self, remark, work_phone, limit_num):
        with self.Session() as session:
            try:
                # 查询电话号码
                target_phone_results = session.query(
                    TargetAccountSportYf3
                ).filter(
                    TargetAccountSportYf3.work_phone == None,
                    TargetAccountSportYf3.remark == remark,
                    TargetAccountSportYf3.is_found == 0,
                ).with_for_update(
                ).limit(limit_num).all()
                phone_numbers = []
                if target_phone_results:
                    # 转换为所需的格式 ['***********', '***********']
                    for target_phone in target_phone_results:
                        target_phone.is_found = 1  # 更新状态为处理中
                        target_phone.update_time = datetime.datetime.now()
                        target_phone.work_phone = work_phone
                        phone_numbers.append(target_phone.target_phone)
                    session.commit()
                    return phone_numbers  # 返回 ['***********', '***********'] 格式
                return []
            except Exception as e:
                session.rollback()
                raise

    # pull group --------------------------------------------------------------------------------
    def pull_group_get_target_users(self, task_id, limit: int):
        with self.Session() as session:
            try:
                target_users: list[PullGroup] = session.query(PullGroup).filter(
                    PullGroup.is_pull == 0,
                    PullGroup.task_id == task_id
                ).with_for_update().limit(limit).all()

                if target_users:
                    for target_info in target_users:
                        target_info.is_pull = 1
                    session.commit()

                    return target_users
                return False
            except NoResultFound:
                session.rollback()
                return []
            except Exception as e:
                session.rollback()
                raise e

    def pull_group_get_task_count(self, task_id):
        """获取任务数量"""
        with self.Session() as session:
            task_count = session.query(PullGroup).filter(PullGroup.task_id == task_id,
                                                         PullGroup.pull_status != None).count()
            return task_count

    def pull_group_update_pull_status(self, data: dict):
        with self.Session() as session:
            session.query(PullGroup).filter(PullGroup.target_user_id == data.get('target_user_id')).update(
                {
                    PullGroup.pull_status: data.get('pull_status'),
                    PullGroup.is_pull: data.get('is_pull'),
                    PullGroup.target_user_pull_time: datetime.datetime.now(),
                    PullGroup.update_time: datetime.datetime.now(),
                    PullGroup.pull_group_id: data.get('pull_group_id'),
                    PullGroup.local_group_username: data.get('local_group_username'),
                    PullGroup.local_group_id: data.get('local_group_id'),
                    PullGroup.local_group_title: data.get('local_group_title'),
                    PullGroup.pull_type: data.get('pull_type'),
                    PullGroup.work_phone: data.get('work_phone'),
                    PullGroup.target_phone: data.get('target_phone')
                })
            session.commit()

    def pull_group_update_is_pull(self, data: dict):
        with self.Session() as session:
            query: PullGroup = session.query(PullGroup).filter(PullGroup.target_user_id == data.get('target_user_id'),
                                                               PullGroup.task_id == data.get('task_id')).first()
            if query and query.pull_status is None:
                query.is_pull = data.get('is_pull')
                query.pull_status = data.get('pull_status')
                query.update_time = datetime.datetime.now()
                session.commit()

    # check_group_res --------------------------------------------------------------------------------
    def check_msg_get_need_check_group_username(self):
        with self.Session() as session:
            need_check_group = session.query(PlanCheckGroup).filter(
                PlanCheckGroup.is_collect == 0).first()
            return need_check_group

    def check_msg_update_is_collect(self, data: dict):
        with self.Session() as session:
            session.query(PlanCheckGroup).filter(PlanCheckGroup.group_username == data.get('group_username')).update(
                {PlanCheckGroup.is_collect: data.get('is_collect')})
            session.commit()

    def check_group_res_update_check_res(self, data: dict):
        with self.Session() as session:
            session.query(PlanCheckGroupRes).filter(
                PlanCheckGroupRes.group_username == data.get('group_username')).update(
                {
                    PlanCheckGroupRes.check_status: data.get('check_status'),
                    PlanCheckGroupRes.in_keywords: data.get('in_keywords'),
                    PlanCheckGroupRes.is_ad_group: data.get('is_ad_group'),
                    PlanCheckGroupRes.score: data.get('score'),
                    PlanCheckGroupRes.reason: data.get('reason'),
                    PlanCheckGroupRes.keywords_score: data.get('keywords_score'),
                    PlanCheckGroupRes.find_keywords: data.get('find_keywords'),
                    PlanCheckGroupRes.error_message: data.get('error_message'),
                    PlanCheckGroupRes.check_finish_time: data.get('check_finish_time')
                })
            session.commit()

    def check_group_res_update_check_status(self, data: dict):
        with self.Session() as session:
            session.query(PlanCheckGroupRes).filter(
                PlanCheckGroupRes.group_username == data.get('group_username')).update(
                {
                    PlanCheckGroupRes.check_status: data.get('check_status'),
                })
            session.commit()

    # tdata bak --------------------------------------------------------------------------------
    def tdata_bak_insert_info(self, data: dict):
        with self.Session() as session:
            try:
                session.add(TdataBak(**data))
                session.commit()
            except Exception as e:
                print(e)
                session.rollback()
                return False

    def tdata_bak_update_zip_status(self, data: dict):
        with self.Session() as session:
            session.query(TdataBak).filter(TdataBak.work_phone == data.get('work_phone')).update(
                {TdataBak.is_zip: 1,
                 TdataBak.zip_pwd: data.get('zip_pwd'),
                 TdataBak.zip_time: datetime.datetime.now()})
            session.commit()

    def tdata_bak_update_upload_status(self, data: dict):
        with self.Session() as session:
            session.query(TdataBak).filter(TdataBak.work_phone == data.get('work_phone')).update(
                {TdataBak.is_upload_network: 1,
                 TdataBak.upload_time: datetime.datetime.now()})
            session.commit()

    def tdata_bak_check_phone_bak(self, work_phone):
        with self.Session() as session:
            try:
                res = session.query(TdataBak).filter(TdataBak.work_phone == work_phone).first()
                if res:
                    return True
                else:
                    return False
            except Exception as e:
                return False

    # collection group --------------------------------------------------------------------------------
    def collection_group_get_group(self, work_phone):
        with self.Session() as session:
            collection_groups = session.query(CollectionGroup).filter(CollectionGroup.work_phone == work_phone,
                                                                      CollectionGroup.group_status == 1).all()
            return collection_groups

    def collection_group_update_group_start_msg_id(self, data: dict):
        with self.Session() as session:
            session.query(CollectionGroup).filter(
                CollectionGroup.group_username == data.get('group_username')).update(
                {CollectionGroup.start_msg_id: data.get('start_msg_id')})
            session.commit()

    def collection_group_is_exist(self, group_id):
        with self.Session() as session:
            count = session.query(CollectionGroup).filter(CollectionGroup.group_id == group_id,
                                                          CollectionGroup.group_status == 1).count()
            if count == 1:
                return True
            if count == 0:
                return True
            else:
                return False

    def collection_group_update_group_info(self, data: dict):
        with self.Session() as session:
            session.query(CollectionGroup).filter(
                CollectionGroup.group_username == data.get('group_username')).update(
                {
                    CollectionGroup.group_id: data.get('group_id'),
                    CollectionGroup.group_title: data.get('group_title'),
                    CollectionGroup.group_username: data.get('group_username'),
                    CollectionGroup.group_count: data.get('group_count'),
                    CollectionGroup.group_online_count: data.get('group_online_count'),
                    CollectionGroup.group_about: data.get('group_about'),
                    CollectionGroup.update_time: datetime.datetime.now()
                })
            session.commit()

    def collection_group_update_group_status(self, data: dict):
        with self.Session() as session:
            session.query(CollectionGroup).filter(
                CollectionGroup.group_username == data.get('group_username')).update(
                {CollectionGroup.group_status: data.get('group_status'),
                 CollectionGroup.update_time: datetime.datetime.now()}
            )
            session.commit()

    def collection_user_insert_user_info(self, user_info_dict):
        """插入用户信息,user_id是唯一索引,如果已存在则不做操作"""
        with self.Session() as session:
            user_info = CollectionUser(**user_info_dict)
            session.add(user_info)
            try:
                session.commit()
            except sqlalchemy.exc.IntegrityError:
                session.rollback()

    def collection_user_group_insert_data(self, data: dict):
        """插入用户与群组关联信息"""
        user_id = data.get('user_id')
        group_id = data.get('group_id')
        with self.Session() as session:
            try:
                new_entry = CollectionUserGroup(user_id=user_id, group_id=group_id)
                session.add(new_entry)
                session.commit()
            except IntegrityError:
                session.rollback()

    # similar channel --------------------------------------------------------------------------------------------------
    def similar_channel_get_source_channel(self, label):
        with self.Session() as session:
            try:
                source_channel = session.query(SimilarChannel).filter(
                    SimilarChannel.status == 0,
                    SimilarChannel.label == label
                ).with_for_update().first()
                if source_channel:
                    source_channel.status = 1
                    session.commit()  # 提交后属性不会过期
                    return source_channel  # 返回时对象属性仍有效
                return None
            except Exception as e:
                session.rollback()
                raise e

    def similar_channel_update_source_channel_info(self, data: dict):
        with self.Session() as session:
            session.query(SimilarChannel).filter(SimilarChannel.channel_id == data.get('channel_id')).update(
                {SimilarChannel.view_discussion: data.get('view_discussion'),
                 SimilarChannel.participants_count: data.get('participants_count')})
            session.commit()

    def similar_channel_update_status(self, data: dict):
        with self.Session() as session:
            session.query(SimilarChannel).filter(SimilarChannel.channel_id == data.get('channel_id')).update(
                {SimilarChannel.status: data.get('status')})
            session.commit()

    def similar_channel_insert_similar_info(self, data: dict):
        with self.Session() as session:
            try:
                similar_channel = SimilarChannel(**data)
                session.add(similar_channel)
                session.commit()
            except IntegrityError as e:
                session.rollback()
                print(f"{data.get('channel_id')} 已存在: {e}")

    def similar_channel_get_similar_channel_for_get_ad(self, label):
        with self.Session() as session:
            try:
                # 计算两小时前的时间点
                two_hours_ago = datetime.datetime.now() - datetime.timedelta(hours=2)

                # 使用FOR UPDATE锁定行，防止并发查询获取相同数据
                similar_channel = session.query(SimilarChannel).filter(
                    (SimilarChannel.label == label) &
                    (SimilarChannel.status == 0) &
                    ((SimilarChannel.update_time == None) |
                     (SimilarChannel.update_time < two_hours_ago))
                ).with_for_update().first()

                if similar_channel:
                    # 立即更新记录的更新时间，防止其他事务选取
                    similar_channel.update_time = datetime.datetime.now()
                    session.commit()
                    return similar_channel
                else:
                    session.commit()
                    return False
            except Exception as e:
                session.rollback()
                print(f"Error in similar_channel_get_similar_channel_for_get_ad: {e}")
                return False

    def similar_channel_update_update_time(self, data):
        with self.Session() as session:
            session.query(SimilarChannel).filter(SimilarChannel.channel_id == data.get('channel_id')).update(
                {SimilarChannel.update_time: datetime.datetime.now()})
            session.commit()

    # sponsored info ---------------------------------------------------------------------------------------------------
    def sponsored_info_insert_sponsored_info(self, data: dict):
        with self.Session() as session:
            try:
                sponsored_info = SponsoredInfo(**data)
                session.add(sponsored_info)
                session.commit()
            except IntegrityError as e:
                session.rollback()
                print(f"{data.get('sponsored_title')} 已存在: {e}")

    # sponsored relationship -------------------------------------------------------------------------------------------
    def sponsored_relationship_insert_info(self, data: dict):
        with self.Session() as session:
            try:
                sponsored_info = SponsoredRelationship(**data)
                session.add(sponsored_info)
                session.commit()
            except IntegrityError as e:
                session.rollback()
                print(f"{data.get('sponsored_id')} 已存在: {e}")

    # forward user -----------------------------------------------------------------------------------------------------
    def forward_user_update_user_premium_permissions(self, data: dict):
        """更新用户拉群权限"""
        user_id = data.get('user_id')
        permissions_invite = data.get('permissions_invite')
        permissions_msg = data.get('permissions_msg')
        mutual_contact = data.get('mutual_contact')
        too_many_pull_group = data.get('permissions_msg')
        with self.Session() as session:
            session.query(ForwardUser).filter(ForwardUser.user_id == user_id).update(
                {ForwardUser.premium_would_allow_invite: permissions_invite,
                 ForwardUser.premium_required_for_pm: permissions_msg,
                 ForwardUser.mutual_contact: mutual_contact,
                 ForwardUser.too_many_pull: too_many_pull_group,
                 ForwardUser.update_time: datetime.datetime.now()})
            session.commit()

    def forward_user_update_save_status(self, data: dict):
        user_id = data.get('user_id')
        save_status = data.get('save_status')
        with self.Session() as session:
            session.query(ForwardUser).filter(ForwardUser.user_id == user_id).update(
                {ForwardUser.save_status: save_status,
                 ForwardUser.update_time: datetime.datetime.now()})
            session.commit()

    def forward_insert_vcard_user_info(self, data):
        with self.Session() as session:
            try:
                session.add(ForwardUser(**data))
                session.commit()
            except IntegrityError as e:
                session.rollback()
                print(f"{data.get('user_id')} 已存在: {e}")

    def forward_user_update_online_time(self, data: dict):
        user_id = data.get('user_id')
        online_time = data.get('user_online_time')
        first_name = data.get('first_name')
        last_name = data.get('last_name')
        username = data.get('username')
        with self.Session() as session:
            session.query(ForwardUser).filter(ForwardUser.user_id == user_id).update(
                {ForwardUser.user_online_time: online_time,
                 ForwardUser.first_name: first_name,
                 ForwardUser.last_name: last_name,
                 ForwardUser.username: username,
                 ForwardUser.update_time: datetime.datetime.now()})
            session.commit()

    def forward_user_get_need_update_online_time_user(self):
        """
        获取需要更新的转发实体(1一条记录)
        查询条件:update_time 在1天以上
        查询到后立即更新update_time时间防止并发时被其他线程查询到
        """
        with self.Session() as session:
            try:
                # 计算一天前的时间点
                one_day_ago = datetime.datetime.now() - datetime.timedelta(days=1)

                # 使用FOR UPDATE锁定行，防止并发查询获取相同数据
                target_user = session.query(ForwardUser).filter(
                    ForwardUser.save_status == 1,  # 只查询存活的用户
                    ForwardUser.forward_type == 2,  # 只查询名片转发的用户
                    (ForwardUser.update_time == None) | (ForwardUser.update_time < one_day_ago)
                ).with_for_update().first()

                if target_user:
                    # 立即更新记录的更新时间，防止其他事务选取
                    target_user.update_time = datetime.datetime.now()
                    session.commit()
                    return target_user
                else:
                    session.commit()
                    return False
            except Exception as e:
                session.rollback()
                print(f"Error in forward_user_get_need_update_online_time_user: {e}")
                return False

    def collection_user_update_forward_user_info(self, data: dict):
        with self.Session() as session:
            session.query(ForwardUser).filter(ForwardUser.user_id == data.get('user_id'),
                                              ForwardUser.label == data.get('label')).update(
                {
                    ForwardUser.username: data.get('username'),
                    ForwardUser.first_name: data.get('first_name'),
                    ForwardUser.last_name: data.get('last_name'),
                    ForwardUser.save_status: data.get('save_status'),
                    ForwardUser.forward_type: data.get('forward_type'),
                    ForwardUser.forward_group_title: data.get('forward_group_title'),
                    ForwardUser.forward_group_link: data.get('forward_group_link'),
                    ForwardUser.forward_msg_id: data.get('forward_msg_id'),
                    ForwardUser.label: data.get('label'),
                    ForwardUser.source: data.get('source'),
                    ForwardUser.update_time: data.get('update_time'),
                    ForwardUser.create_time: data.get('create_time')
                })
            session.commit()

    def collection_user_update_forward_user_status(self, data: dict):
        with self.Session() as session:
            session.query(ForwardUser).filter(ForwardUser.user_id == data.get('user_id'),
                                              ForwardUser.label == data.get('label')).update(
                {
                    ForwardUser.username: data.get('username'),
                    ForwardUser.first_name: data.get('first_name'),
                    ForwardUser.last_name: data.get('last_name'),
                    ForwardUser.save_status: data.get('save_status'),
                    ForwardUser.forward_type: data.get('forward_type'),
                    ForwardUser.forward_group_title: data.get('forward_group_title'),
                    ForwardUser.forward_group_link: data.get('forward_group_link'),
                    ForwardUser.forward_msg_id: data.get('forward_msg_id'),
                    ForwardUser.label: data.get('label'),
                    ForwardUser.source: data.get('source'),
                    ForwardUser.update_time: data.get('update_time'),
                    ForwardUser.create_time: data.get('create_time')
                })
            session.commit()

    # init create group ------------------------------------------------------------------------------------------------

    def init_create_group_insert_group_info(self, data: dict):
        with self.Session() as session:
            group_info = InitCreateGroup(**data)
            session.add(group_info)
            session.commit()

    def init_create_group_get_raise_group(self):
        with self.Session() as session:
            group_info: list[InitCreateGroup] = session.query(InitCreateGroup).filter(
                InitCreateGroup.create_status == 2).all()
            return group_info

    def init_create_group_update_group_status(self, data: dict):
        with self.Session() as session:
            session.query(InitCreateGroup).filter(InitCreateGroup.group_link == data.get('group_link')).update(
                {InitCreateGroup.create_status: 3})
            session.commit()

    # def init_greate_group_update_create_status_1(self, data: dict):
    #     with self.Session() as session:
    #         session.query(InitCreateGroup).filter(InitCreateGroup.group_id == data.get('group_id')).update(
    #             {InitCreateGroup.create_status: data.get('create_status'),
    #              InitCreateGroup.group_link: data.get('group_link'),
    #              InitCreateGroup.group_title: data.get('group_title'),
    #              InitCreateGroup.work_phone: data.get('work_phone'),
    #              InitCreateGroup.create_time: datetime.datetime.now()}
    #         )
    #         session.commit()

    def init_greate_group_update_create_status_2(self, data: dict):
        with self.Session() as session:
            session.query(InitCreateGroup).filter(InitCreateGroup.group_id == data.get('group_id')).update(
                {InitCreateGroup.create_status: data.get('create_status'),
                 InitCreateGroup.group_link: data.get('group_link')}
            )
            session.commit()

    def init_create_group_get_work_account_group_count(self, work_phone):
        with self.Session() as session:
            work_account_groups = session.query(InitCreateGroup).filter(
                InitCreateGroup.work_phone == work_phone).all()
            return work_account_groups

    def init_create_group_check_work_account_group_count(self, work_phone, count):
        with self.Session() as session:
            work_account_groups = session.query(InitCreateGroup).filter(
                InitCreateGroup.work_phone == work_phone).all()
            if len(work_account_groups) < count:
                return True
            return False

    # sys conf ---------------------------------------------------------------------------------------------------------
    def get_system_conf(self) -> SystemConf | bool:
        """
        获取系统配置
        :return: SystemConf
        """
        with self.Session() as session:
            try:
                system_conf: SystemConf = session.query(SystemConf).first()
                return system_conf
            except sqlalchemy.exc.SQLAlchemyError as e:
                print(f"数据库错误: {e}")
                return False
            except Exception as e:
                print(f"未知错误: {e}")
                return False

    # search task -------------------------------------------------------------------------------------------------------
    def get_search_task(self):
        with self.Session() as session:
            # 计算10天前的时间点
            ten_days_ago = datetime.datetime.now() - datetime.timedelta(days=10)
            
            search_task = session.query(SearchChannelTask).filter(
                SearchChannelTask.search_status == 1,
                ((SearchChannelTask.update_time == None) | (SearchChannelTask.update_time < ten_days_ago))
            ).all()
            return search_task

    def update_search_task(self,task_id):
        with self.Session() as session:
            session.query(SearchChannelTask).filter(SearchChannelTask.id == task_id).update(
                {SearchChannelTask.update_time: datetime.datetime.now()})
            session.commit()

    def search_res_update_group_info(self, data: dict):
        with self.Session() as session:
            group:SearchGroup = session.query(SearchGroup).filter(SearchGroup.group_id == data.get('group_id')).first()
            if group:
                group.title = data.get('title')
                group.keyword = data.get('keyword')
                group.group_type = data.get('group_type')
                group.broadcast = data.get('broadcast')
                group.verified = data.get('verified')
                group.forum = data.get('forum')
                group.megagroup = data.get('megagroup')
                group.channel_info = data.get('channel_info')
                group.username = data.get('username')
                group.label = data.get('label')
                group.update_time = datetime.datetime.now()
                session.commit()
            else:
                new_group = SearchGroup(**data)
                session.add(new_group)
                session.commit()


    # ==================================================================================
    def get_target_phone(self):
        with self.Session() as session:
            target_phone = session.query(TargetAccountSportYf3.target_phone).filter(
                TargetAccountSportYf3.work_phone == None).all()
            return target_phone

    def update_target_phone(self, old_target_phone, target_phone):
        with self.Session() as session:
            session.query(TargetAccountSportYf3).filter(TargetAccountSportYf3.raw_phone == old_target_phone).update(
                {TargetAccountSportYf3.target_phone: target_phone})
            session.commit()

    def find_work_account_test(self,work_phone='************'):
        with self.Session() as session:
            a = session.query(WorkAccount).filter(WorkAccount.work_phone == work_phone,WorkAccount.remark == None).first()
            if a:
                print(a.work_phone)


if __name__ == '__main__':
    db = MySqlAccountFun()
    # db.collection_group_update_group_info(
    #     {'group_username': 'bongda668', 'group_id': **********,
    #      'group_about': '⚽️ Nhóm Kéo Bóng kiếm 2tr -5tr / mỗi ngày ! \n👑 Chốt kèo trong VIP - Chốt trước trận 30p\n💎ADM : https://t.me/Chocopie101',
    #      'group_count': 4540, 'group_online_count': 7, 'group_title': 'BÓNG ĐÁ-XỔ SỐ 3 MIỀN'})

    # account_list = []
    # accounts: list[WorkAccount] = db.get_work_accounts()
    # for account in accounts:
    #     if account.account_type == 'collection':
    #         print(account.__dict__)
    #         account_list.append(account.__dict__)
    # print(len(account_list))

    # res = db.collection_user_get_forward_msg('SZ399001')
    # print(res)

    res = db.get_work_accounts_by_account_type('update_online_time')
    print(res)

