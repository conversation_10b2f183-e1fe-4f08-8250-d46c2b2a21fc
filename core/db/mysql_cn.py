from sqlalchemy import Column, Integer, String, SmallInteger, DateTime, Text, BigInteger, create_engine, \
    UniqueConstraint, Index, func, Date, JSON
from sqlalchemy.orm import sessionmaker, scoped_session
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()


class WorkAccount(Base):
    __tablename__ = 'work_account'
    __table_args__ = (UniqueConstraint('work_phone', name='work_phone'),)

    id = Column(Integer, primary_key=True, autoincrement=True)
    work_phone = Column(String(255), nullable=True, comment='账号电话号码')
    country = Column(String(10), nullable=True, comment='国家代码')
    user_id = Column(Integer, nullable=True, comment='工作账号id')
    username = Column(String(255), nullable=True, comment='工作账号用户名')
    first_name = Column(String(255), nullable=True, comment='first_name.txt')
    last_name = Column(String(255), nullable=True, comment='last_name')
    is_avatar = Column(SmallInteger, default=0, comment='是否设置头像')
    is_about = Column(SmallInteger, default=0, comment='是否设置简介')
    is_joined_all_group = Column(SmallInteger, default=0, comment='是否加入养号总群')
    is_clear_session = Column(SmallInteger, default=0, comment='是否清除其他session')
    is_init_forward_msg = Column(SmallInteger, default=0, comment='是否初始化消息模板(0需要初始化,1完成初始化)')
    can_use_time = Column(DateTime, nullable=True, comment='可用时间')
    reg_time = Column(DateTime, nullable=True, comment='账号注册时间')
    proxy_host = Column(String(255), nullable=True, comment='代理地址')
    proxy_port = Column(Integer, nullable=True, comment='代理端口')
    proxy_hash = Column(String(33), nullable=True, comment='代理hash')
    use_status = Column(SmallInteger, default=0, comment='账号工作状态:0:空闲;1,队列中;2:工作中;99:异常状态;')
    over_task_status = Column(SmallInteger, default=1,
                              comment='账号结束任务时状态:任务结束时状态:1:正常 2:探号超限 3: 发送超限 4::账号异常 5 账号被投诉 6 没有可用发送账号 7 账号被投诉,不能创建会话')
    spam_bot_check_status = Column(SmallInteger, default=0, comment='spam机器人检测状态0:正常,1:账号异常,2:被投诉')
    spam_bot_check_time = Column(DateTime, nullable=True, comment='spam检测时间')
    next_can_raise_time = Column(DateTime, nullable=False, comment='下次可养号时间')
    next_can_send_time = Column(DateTime, nullable=False, comment='下次可发送时间')
    next_can_find_time = Column(DateTime, nullable=False, comment='下次可探号时间')
    last_send_time = Column(DateTime, nullable=True, comment='最后一次发送消息时间')
    last_find_time = Column(DateTime, nullable=True, comment='最后一次探号时间')
    last_pull_time = Column(DateTime, nullable=True, comment='最后一次拉群时间')
    send_count = Column(Integer, default=0, comment='发送数量')
    is_first_find_account_over_limit = Column(SmallInteger, default=0, comment='是否第一次超限')
    find_count = Column(Integer, default=0, comment='探号数量')
    raise_contacts_num = Column(Integer, default=0, comment='养号添加好友数')
    death_time = Column(DateTime, nullable=True, comment='死号时间')
    death_reason = Column(Text, nullable=True, comment='死号报错')
    save_status = Column(SmallInteger, default=1, comment='工作账号存活状态')
    msg_channel_title = Column(String(255), nullable=True, comment='转发消息频道title')
    msg_channel_link = Column(String(255), nullable=True, comment='转发消息频道link')
    msg_channel_id = Column(Integer, nullable=True, comment='转发消息频道id')
    source = Column(String(255), nullable=True, comment='账号来源')
    remark = Column(Text, nullable=True, comment='备注')
    create_time = Column(DateTime, nullable=True, comment='账号上传时间')
    account_type = Column(String(255), nullable=True, comment='账号类型,指定账号任务')


class WorkAccountStatusAddContact(Base):
    __tablename__ = 'work_account_status_add_contact'

    id = Column(Integer, primary_key=True)
    work_phone = Column(String(255), nullable=True, comment='电话号码')
    contacts_count = Column(Integer, nullable=True, comment='通讯录好友数量')
    add_contacts_count = Column(Integer, nullable=True, comment='探号数量')
    add_contact_success_contact_count = Column(Integer, nullable=True, comment='探号成功数量')
    add_contacts_error = Column(Integer, nullable=True, comment='探号失败数量')
    add_contacts_label = Column(Integer, nullable=True, comment='探号标签数量')
    add_contacts_time = Column(DateTime, nullable=True, comment='探号时间')


class WorkAccountStatusPullGroup(Base):
    __tablename__ = 'work_account_status_pull_group'

    id = Column(Integer, primary_key=True, nullable=False)
    work_phone = Column(String(100), nullable=True)
    pull_status = Column(String(20), nullable=True)
    error = Column(Text, nullable=True)
    pull_time = Column(DateTime, nullable=True)


class WorkAccountJoinGroup(Base):
    __tablename__ = 'work_account_join_group'

    id = Column(Integer, primary_key=True, autoincrement=True)
    work_phone = Column(String(255, collation='utf8mb4_bin'), nullable=False, comment='工作账号电话号码')
    group_username = Column(String(255, collation='utf8mb4_bin'), nullable=False, comment='群用户名')
    join_status = Column(String(255, collation='utf8mb4_bin'), default='1', comment='加群状态0未加入,1已加入')
    join_time = Column(DateTime, comment='加入时间')
    update_time = Column(DateTime, comment='更新时间')

    __table_args__ = (
        UniqueConstraint('work_phone', 'group_username', name='w_g'),
        Index('work_phone', 'work_phone'),
        Index('group_username', 'group_username'),
    )


class CollectionGroup(Base):
    __tablename__ = 'collection_group'

    id = Column(Integer, primary_key=True, autoincrement=True)
    group_username = Column(String(100), nullable=True)
    group_id = Column(BigInteger, nullable=True, unique=True)
    work_phone = Column(String(30), nullable=True)
    group_label = Column(String(255), nullable=True, comment='群标签')
    group_status = Column(SmallInteger, default=1, comment='群可采集状态1:可用,2:未找到群')
    start_msg_id = Column(Integer, default=1, comment='采集起始消息id')
    group_about = Column(Text, nullable=True, comment='群简介')
    group_title = Column(String(255), nullable=True, comment='群标题')
    group_count = Column(BigInteger, nullable=True, comment='群人数')
    group_online_count = Column(BigInteger, nullable=True, comment='群在线人数')
    create_time = Column(DateTime, nullable=True, comment='创建时间')
    update_time = Column(DateTime, nullable=True,
                         comment='更新时间')


class CollectionUser(Base):
    __tablename__ = 'collection_user'

    id = Column(Integer, primary_key=True, autoincrement=True, comment='主键ID')
    user_id = Column(BigInteger, unique=True, nullable=False, comment='用户ID')
    username = Column(String(255, collation='utf8mb4_bin'), comment='用户名')
    premium = Column(SmallInteger, comment='是否为大会员')
    collection_group_id = Column(BigInteger, comment='收藏群组ID')
    collection_group_username = Column(String(255, collation='utf8mb4_bin'), comment='收藏群组用户名')
    collection_msg_id = Column(BigInteger, comment='收藏消息ID')
    forward_group_id = Column(BigInteger, comment='转发群组ID')
    forward_group_username = Column(String(255, collation='utf8mb4_bin'), comment='转发群组用户名')
    forward_msg_id = Column(BigInteger, comment='转发消息ID')
    create_time = Column(DateTime, comment='创建时间')
    update_time = Column(DateTime, comment='更新时间')
    premium_would_allow_invite = Column(SmallInteger, comment='1:只有高级用户可以邀请入群;0:任何用户不得邀请入群')
    premium_required_for_pm = Column(SmallInteger, comment='1:只有高级用户可以私聊;0:任何用户可以对该用户私聊')
    pull_status = Column(SmallInteger, comment='1:进群成功 2:拉群失败')
    label = Column(String(255, collation='utf8mb4_bin'), comment='标签')


class CollectionUserGroup(Base):
    __tablename__ = 'collection_user_group'

    id = Column(Integer, primary_key=True, nullable=False)
    user_id = Column(BigInteger, nullable=True)
    group_id = Column(BigInteger, nullable=True)

    # 定义唯一性约束
    __table_args__ = (
        UniqueConstraint('user_id', 'group_id', name='uix_user_id_group_id'),
    )


class PullGroup(Base):
    __tablename__ = 'pull_group'

    id = Column(Integer, primary_key=True, autoincrement=True)
    project_name = Column(String(255), nullable=True, comment='项目名称')
    project_subcategory = Column(String(255), nullable=True, comment='项目子类')
    work_phone = Column(String(255), nullable=True, comment='工作账号')
    local_group_title = Column(String(255), nullable=True, comment='本地群title')
    local_group_username = Column(String(255), nullable=True, comment='本地群username')
    local_group_id = Column(BigInteger, nullable=True, comment='本地群id')
    pull_type = Column(String(255), nullable=True, comment='拉群方式')
    pull_group_title = Column(String(255), nullable=True, comment='目标群title')
    pull_group_username = Column(String(255), nullable=True, comment='目标群username')
    pull_group_id = Column(BigInteger, nullable=True, comment='目标群id')
    target_username = Column(String(255), nullable=True, comment='目标用户名')
    target_user_id = Column(BigInteger, nullable=True, comment='目标用户id')
    was_online_time = Column(DateTime, nullable=True, comment='目标用户上次在线时间')
    target_user_pull_time = Column(DateTime, nullable=True, comment='目标用户拉群时间')
    update_time = Column(DateTime, nullable=True, onupdate=lambda ctx: ctx.current_parameters['update_time'],
                         comment='跟新时间')
    task_id = Column(Integer, nullable=True, comment='任务id')
    is_pull = Column(SmallInteger, default=0, comment='是否执行过拉群')
    pull_status = Column(String(255), nullable=True, comment='请求邀请用户到群状态')
    pull_group_msg_id = Column(BigInteger, nullable=True, comment='消息id')
    target_phone = Column(String(50), nullable=True)


class Contact(Base):
    __tablename__ = 'contacts'

    id = Column(Integer, primary_key=True, autoincrement=True)
    work_phone = Column(String(20), nullable=True, comment='工作账号电话')
    target_phone = Column(String(20), nullable=False, comment='目标用户电话')
    user_id = Column(BigInteger, nullable=True, comment='用户id')
    username = Column(String(100), nullable=True, comment='username')
    mutual_contact = Column(SmallInteger, nullable=True, comment='是否互为好友')
    user_was_online_time = Column(DateTime, nullable=True, comment='上次登录时间')
    check_online_time = Column(DateTime, nullable=True, comment='上次登录检测时间')
    label = Column(String(255), nullable=True, comment='目标账号分类')
    forward_group_username = Column(String(255), nullable=True, comment='转发名片群')
    forward_group_title = Column(String(255), nullable=True)
    forward_group_msg_id = Column(Integer, nullable=True, comment='转发名片消息id')
    find_time = Column(DateTime, nullable=True, comment='探号时间')
    update_time = Column(DateTime, nullable=True, default=func.now(), onupdate=func.now(), comment='账号信息更新时间')


class TargetAccount(Base):
    __tablename__ = 'target_account'

    id = Column(Integer, primary_key=True, autoincrement=True)
    raw_phone = Column(String(255), unique=True, nullable=True, comment='原始目标号码')
    target_phone = Column(String(255), unique=True, nullable=True, comment='目标号码')
    is_found = Column(SmallInteger, default=0, comment='是否已经探测0:未探测;1:正在使用;2:探测完毕')
    is_tg_account = Column(SmallInteger, default=0, comment='是否是tg用户')
    work_phone = Column(String(255), nullable=True, comment='工作账号电话号码')
    found_time = Column(DateTime, nullable=True, comment='探测时间')
    create_time = Column(DateTime, nullable=True, comment='创建时间')
    update_time = Column(DateTime, nullable=True, onupdate=lambda ctx: ctx.current_parameters['update_time'],
                         comment='更新时间')
    remark = Column(Text, nullable=True, comment='备注')


class TargetAccountSportYf3(Base):
    __tablename__ = 'target_account_sport_yf_3'

    id = Column(Integer, primary_key=True, autoincrement=True)
    raw_phone = Column(String(255), unique=True, nullable=True, comment='原始目标号码')
    target_phone = Column(String(255), unique=True, nullable=True, comment='目标号码')
    is_found = Column(SmallInteger, default=0, comment='是否已经探测0:未探测;1:正在使用;2:探测完毕')
    is_tg_account = Column(SmallInteger, default=0, comment='是否是tg用户')
    work_phone = Column(String(255), nullable=True, comment='工作账号电话号码')
    found_time = Column(DateTime, nullable=True, comment='探测时间')
    create_time = Column(DateTime, nullable=True, comment='创建时间')
    update_time = Column(DateTime, nullable=True, onupdate=lambda ctx: ctx.current_parameters['update_time'],
                         comment='更新时间')
    remark = Column(Text, nullable=True, comment='备注')


class MiniAppToken(Base):
    __tablename__ = 'mini_app_token'

    id = Column(Integer, primary_key=True, autoincrement=True)
    work_phone = Column(String(255), nullable=True)
    user_id = Column(BigInteger, nullable=True)
    bot_username = Column(String(255), nullable=True)
    mini_app_name = Column(String(255), nullable=True)
    token = Column(Text, nullable=True)
    user_status = Column(String(255), nullable=True, comment='使用方')
    create_time = Column(DateTime, nullable=True)


class PlanCheckGroup(Base):
    __tablename__ = 'plan_check_group'

    id = Column(Integer, primary_key=True, autoincrement=True)
    group_title = Column(String(255), nullable=True)
    group_username = Column(String(255), nullable=True, unique=True)
    group_language = Column(String(255), nullable=True)
    is_collect = Column(SmallInteger, nullable=True)
    is_ad_group = Column(SmallInteger, nullable=True, comment='是否是广告群')
    collect_time = Column(DateTime, nullable=True)
    label = Column(String(255), nullable=True, comment='标签')


class PlanCheckGroupRes(Base):
    __tablename__ = 'plan_check_group_res'

    id = Column(Integer, primary_key=True, autoincrement=True)
    group_username = Column(String(255), nullable=True)
    check_status = Column(SmallInteger, nullable=True, comment='0为已提交检测,1为检测完毕')
    is_ad_group = Column(SmallInteger, nullable=True)
    in_keywords = Column(Text, nullable=True)
    score = Column(Integer, nullable=True, comment='检测分数')
    reason = Column(Text, nullable=True)
    primary_language = Column(String(20), nullable=True)
    keywords_score = Column(Integer, nullable=True)
    find_keywords = Column(Text, nullable=True)
    error_message = Column(Text, nullable=True)
    create_time = Column(DateTime, nullable=True)
    check_finish_time = Column(DateTime, nullable=True)
    project_name = Column(String(255), nullable=True)


class TdataBak(Base):
    __tablename__ = 'tdata_bak'

    id = Column(Integer, primary_key=True, autoincrement=True)
    work_phone = Column(String(255), unique=True, nullable=True)
    zip_pwd = Column(String(255), nullable=True)
    is_zip = Column(SmallInteger, nullable=True)
    zip_time = Column(DateTime, nullable=True)
    is_upload_network = Column(SmallInteger, nullable=True)
    upload_time = Column(DateTime, nullable=True)


class SimilarChannel(Base):
    __tablename__ = 'similar_channel'

    id = Column(Integer, primary_key=True, autoincrement=True)
    channel_link = Column(String(100), comment='群用户名', default=None)
    channel_id = Column(BigInteger, unique=True, index=True)
    source_channel_link = Column(String(100), default=None)
    participants_count = Column(Integer, comment='订阅者数量', default=0)
    view_discussion = Column(Text, comment='讨论组用户名')
    status = Column(SmallInteger, comment='搜索状态:0:未搜索,1:已搜索', default=0)
    label = Column(String(100), comment='标签', default=None)
    search_time = Column(DateTime, default=None)
    update_time = Column(DateTime, default=None)


class SponsoredInfo(Base):
    __tablename__ = 'sponsored_info'

    id = Column(Integer, primary_key=True, autoincrement=True)
    source_username = Column(String(100), nullable=True)
    sponsored_message_md5 = Column(String(255), nullable=True)
    sponsored_message = Column(Text, nullable=True, comment='广告消息')
    sponsored_title = Column(String(255), nullable=True, comment='广告主体title')
    sponsored_url = Column(String(255), nullable=True, comment='广告主体原始推广链接')
    sponsored_link = Column(String(100), nullable=True, comment='广告主体用户名')
    sponsored_id = Column(Integer, nullable=True, comment='广告主体id')
    sponsored_type = Column(Integer, nullable=True, comment='广告主体类型,1:机器人;2:公开频道;3:私有频道')
    sponsored_language = Column(String(10), nullable=True, comment='广告主体语言')
    label = Column(String(50), nullable=True)
    create_time = Column(DateTime, nullable=True, default=func.now())

    # 定义唯一约束
    __table_args__ = (
        UniqueConstraint('source_username', 'sponsored_message_md5', name='msg'),
    )


class SponsoredRelationship(Base):
    __tablename__ = 'sponsored_relationship'

    id = Column(Integer, primary_key=True, autoincrement=True)
    source_channel_id = Column(BigInteger, default=None)
    sponsored_id = Column(BigInteger, default=None)
    sponsored_date = Column(Date, default=None)

    # 定义唯一约束
    __table_args__ = (
        UniqueConstraint('sponsored_id', 'sponsored_date', name='relationship'),
    )


class ForwardUser(Base):
    __tablename__ = 'forward_users'

    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(BigInteger, default=None)
    username = Column(String(100), default=None)
    first_name = Column(String(50), default=None)
    last_name = Column(String(50), default=None)
    about = Column(Text, default=None)
    user_online_time = Column(DateTime, default=None)
    save_status = Column(SmallInteger, default=1, comment='存活状态')
    forward_type = Column(SmallInteger, default=None, comment='转发类型1,消息   2,名片')
    forward_group_title = Column(String(255), default=None)
    forward_group_link = Column(String(255), default=None)
    forward_msg_id = Column(BigInteger, default=None)
    label = Column(String(255), default=None)
    source = Column(String(255), default=None)
    update_time = Column(DateTime, default=None)
    create_time = Column(DateTime, default=None)
    premium_would_allow_invite = Column(SmallInteger, comment='1:只有高级用户可以邀请入群;0:任何用户不得邀请入群')
    premium_required_for_pm = Column(SmallInteger, comment='1:只有高级用户可以私聊;0:任何用户可以对该用户私聊')
    mutual_contact = Column(SmallInteger, comment='1:需要双向好友可以邀请用户入群;0没有限制')
    too_many_pull = Column(SmallInteger, comment='1:拉取次数过多')


class InitCreateGroup(Base):
    __tablename__ = 'init_create_group'

    id = Column(Integer, primary_key=True, autoincrement=True)
    group_title = Column(String(100), default=None)
    group_link = Column(String(255), default=None)
    group_id = Column(Integer, default=None)
    work_phone = Column(String(30), default=None)
    create_time = Column(DateTime, default=None)
    create_status = Column(SmallInteger, default=None, comment='1,私有群;2,公开群')


class SystemConf(Base):
    """系统配置表"""
    __tablename__ = 'system_conf'

    id = Column(Integer, primary_key=True, autoincrement=True, comment='主键ID')
    proxy_risk_check_enable = Column(SmallInteger, default=1,
                                     comment='是否启用代理ip威胁值过滤（1为启用，0为不启用）')
    proxy_risk_grade = Column(String(10), default=None,
                              comment='过滤等级（\'low\', \'medium\', \'high\'）')
    proxy_can_use_count = Column(Integer, comment='代理ip最大使用次数')

    def __repr__(self):
        return f"<SystemConf(id={self.id}, proxy_risk_check_enable={self.proxy_risk_check_enable}, " \
               f"proxy_risk_grade={self.proxy_risk_grade})>"


class SearchChannelTask(Base):
    __tablename__ = 'search_channel_task'

    id = Column(Integer, primary_key=True, nullable=False)
    search_keywords = Column(String(32), unique=True, nullable=True, comment='搜索关键词')
    search_status = Column(SmallInteger, default=1, nullable=True, comment='是否启用搜索0:不启用,1:启用')
    create_time = Column(DateTime, nullable=True, default=func.now())
    update_time = Column(DateTime, nullable=True, onupdate=func.now())
    label = Column(String(50), nullable=True, comment='标签')

class SearchGroup(Base):
    __tablename__ = 'search_group'

    id = Column(Integer, primary_key=True, nullable=False,autoincrement=True)
    keyword = Column(String(255), nullable=True, comment='搜索关键词')
    title = Column(String(255), nullable=True)
    username = Column(String(100), nullable=True)
    group_id = Column(BigInteger, primary_key=True, nullable=False)
    group_type = Column(SmallInteger, nullable=True, comment='0:频道  1:群组')
    verified = Column(SmallInteger, nullable=True, comment='0:未认证  1:已认证')
    broadcast = Column(SmallInteger, nullable=True, comment='0:非广播频道  1:广播频道')
    forum = Column(SmallInteger, nullable=True, comment='0:非论坛  1:论坛')
    megagroup = Column(SmallInteger, nullable=True, comment='0:非超级群主  1:超级群主')
    participants_count = Column(Integer, nullable=True, comment='群人数')
    about = Column(Text, nullable=True, comment='群简介')
    language = Column(String(10), nullable=True, comment='群语言')
    label = Column(String(100), nullable=True, comment='标签')
    remark = Column(String(255), nullable=True, comment='备注')
    update_time = Column(DateTime, nullable=True)
    create_time = Column(DateTime, nullable=True)
    channel_info = Column(Text, nullable=True)


class DBSession:
    def __init__(self):
        db_host = '************:3306'
        db_username = 'ins_admin'
        db_password = 'dEjediPj91T5KekmUnMa'
        db_name = 'tg_v2.1'

        # 初始化 DBSession 对象
        db_url = f"mysql+pymysql://{db_username}:{db_password}@{db_host}/{db_name}?charset=utf8mb4"
        self.engine = create_engine(db_url, echo=False, pool_size=50, max_overflow=50, pool_timeout=600,
                                    pool_recycle=1800)
        self.session_factory = sessionmaker(bind=self.engine, expire_on_commit=False)
        self.Session = scoped_session(self.session_factory)
        Base.metadata.create_all(self.engine)


if __name__ == '__main__':
    db = DBSession()
    pool = db.engine.pool
    db.engine.dispose()
    print(pool.status())
