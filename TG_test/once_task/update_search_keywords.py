import os
import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

from core.db.mysql_fun import MySqlAccountFun
from core.db.mysql_cn import SearchChannelTask, DBSession
from nb_log import get_logger
import datetime
from conf.conf import base_set

# 初始化日志
logger = get_logger(name=f'{datetime.date.today()}',
                    log_path=f'{base_set.log_dir}',
                    log_filename=f'{datetime.date.today()}.log')

db = DBSession()

# 关键词文件路径
KEYWORDS_FILE = os.path.join(project_root, 'conf', 'search_keywords.txt')

def read_keywords_from_file():
    """从文件中读取关键词"""
    if not os.path.exists(KEYWORDS_FILE):
        logger.error(f"关键词文件不存在: {KEYWORDS_FILE}")
        return []
    
    with open(KEYWORDS_FILE, 'r', encoding='utf-8') as f:
        keywords = [line.strip() for line in f.readlines() if line.strip()]
    
    logger.info(f"从文件中读取了 {len(keywords)} 个关键词")
    return keywords

def update_keywords_to_db(keywords, label):
    """更新关键词到数据库"""
    session_db = db.Session()
    try:
        for keyword in keywords:
            with session_db as session:
                try:
                    existing_task = session.query(SearchChannelTask).filter(SearchChannelTask.search_keywords==keyword).first()
                    if existing_task:
                        existing_task.search_status = 1
                        existing_task.update_time = datetime.datetime.now()
                        existing_task.label = label
                        session.commit()
                        logger.info(f"更新关键词 {keyword} 的状态为启用")
                    else:
                        new_task = SearchChannelTask(search_keywords=keyword, search_status=1, label=label)
                        session.add(new_task)
                        session.commit()
                        logger.info(f"添加新关键词 {keyword} 到数据库")
                except Exception as e:
                    session.rollback()
                    logger.error(f"更新关键词 {keyword} 时出错: {str(e)}")
    

    
    finally:
        session.close()

def main():
    logger.info("开始更新搜索关键词到数据库")
    keywords = read_keywords_from_file()
    if not keywords:
        logger.warning("没有找到关键词，请检查文件是否存在且不为空")
        return
    
    update_keywords_to_db(keywords, label='web3_交易所')

if __name__ == '__main__':
    main()