import asyncio
import datetime
import random
import time

import pytz
from nb_log import get_logger
from telethon import TelegramClient
from telethon.tl.custom import Dialog
from telethon.tl.functions.channels import SearchPostsRequest
from telethon.tl.functions.contacts import SearchRequest
# from telethon.tl.functions.messages import SearchRequest, SearchGlobalRequest
from telethon.tl.types import User, UserStatusRecently, UserStatusLastWeek, UserStatusLastMonth, UserStatusOnline, \
    UserStatusOffline, InputPeerEmpty, InputMessagesFilterEmpty, TypeInputPeer
from telethon.tl.types.contacts import Found

from TG_account_manager_v2_1.base_action.create_client import CreateClient
from TG_test.base_test.create_client import get_client
from conf.conf import base_set
from core.db.mysql_cn import DBSession, PullGroup, WorkAccount
import nb_log

from core.db.mysql_fun import MySqlAccountFun

Session = DBSession()




def get_work_account():
    with Session.Session() as session:
        work_account:WorkAccount = session.query(
            WorkAccount
        ).filter(
            WorkAccount.use_status == 0,
            WorkAccount.save_status == 1,
            WorkAccount.remark == None

        ).first()
        if work_account:
            return work_account
        return None

async def search_channel(client: TelegramClient):
    result:Found = await client(SearchRequest(
        q='Solana',
        limit=30
    ))
    chats = result.chats
    print(result.stringify())
    for chat in chats:
        print(chat)
        print('--'*30)


async def run(par,sql_db, logger_init):
    client: TelegramClient = await CreateClient(par, sql_db, logger_init).start_get_client()
    await search_channel(client)

    # enetity = await client.get_entity('Oklivefree')
    # print(enetity)

    await client.disconnect()

if __name__ == '__main__':
    work_accounts_path = '/home/<USER>/save_tdata/42_savetdata/account'
    work_account = get_work_account()
    sql_db = MySqlAccountFun()
    logger_init = get_logger(name=f'{datetime.date.today()}',
                             log_path=f'{base_set.log_dir}',
                             log_filename=f'{datetime.date.today()}.log')
    if work_account:
        print(work_account.__dict__)


        asyncio.run(run(work_account, sql_db, logger_init))


