import asyncio
import datetime
import logging
import random
import string

import telethon
from telethon import functions
from telethon.tl.custom import Dialog
from telethon.tl.functions.channels import InviteToChannelRequest
from telethon.tl.functions.contacts import GetContactsRequest

from telethon.tl.types import Message, Channel, Chat, InputUser, InputChannel, Updates
from telethon.client import TelegramClient
from telethon.tl.types.messages import InvitedUsers

from TG_account_manager_v2_1.base_action.base_action import BaseAction
from TG_account_manager_v2_1.base_fun.base_fun import get_user_was_online_time
from core.db.mysql_cn import PullGroup, WorkAccount
from core.db.redis_fun import RedisFun
from telethon.tl.types import User
from core.db.mysql_fun import MySqlAccountFun
from core.models.work_account_models import AccountModel
from run.boost_mysql import mysql_write_push


class GetTargetUserIntoGrop(BaseAction):
    def __init__(self, par: WorkAccount, db: MySqlAccountFun, redis_fun: RedisFun, logger: logging.Logger,
                 client: TelegramClient):
        # 初始化方法，设置参数，数据库连接和日志记录器
        super().__init__(par, client, logger, db, redis_fun)
        self.par = par
        self.db = db
        self.logger = logger
        self.redis_fun = redis_fun
        self.client: TelegramClient = client

        self.logger.info(
            f'==== 开始拉群 work_phone:{self.par.work_phone} ====')

        self.target_group_title = 'whzl_users'
        self.target_group_username = 'https://t.me/+0SV30BGpThY4M2Rl'
        # self.target_group_title = 'yf_sport'
        # self.target_group_username = 'https://t.me/+xNIP74BhjJxmM2I0'
        # self.target_group_title = 'account_2'
        # self.target_group_username = 'https://t.me/+WU5MbAwV8hJlY2Q0'

        self.target_group_entity = None
        self.activate_time_day = None
        self.target_phone = None

    async def get_users_entity_group(self):
        self.logger.info(f'开始从本地对话获取是一群  {self.target_group_title} {self.target_group_username}')
        dialogs: list[Dialog] = await self.client.get_dialogs()
        # print(dialogs)
        for dialog in dialogs:
            if dialog.name == self.target_group_title:
                self.target_group_entity = dialog.entity
                print('1', self.target_group_entity)
                return dialog.entity

        self.logger.warning(f'没有找到实体群 {self.target_group_title} <UNK> {self.target_group_username} 开始加实体群')
        if await self.join_group(self.target_group_username, True):
            dialogs = await self.client.get_dialogs()
            # print(dialogs)
            for dialog in dialogs:
                if dialog.name == self.target_group_title:
                    print(dialog.entity)

                    return dialog.entity
        return False

    async def get_forward_user_entity(self, group_msg_id):
        msg_entity: Message = await self.client.get_messages(self.target_group_entity, ids=group_msg_id)
        print(self.target_group_entity)
        print(msg_entity)
        if msg_entity:
            if msg_entity.fwd_from.from_id is None:
                self.logger.warning(f'获取目标用户实体失败: group_msg_id:{group_msg_id} 该实体无效')
                return False
            if not hasattr(msg_entity.fwd_from, 'from_id'):
                self.logger.warning(f'获取目标用户实体失败: group_msg_id:{group_msg_id} 该实体无效,没有from_id')
                return False
            if not hasattr(msg_entity.fwd_from.from_id, 'user_id'):
                self.logger.warning(f'获取目标用户实体失败: group_msg_id:{group_msg_id} 该实体无效,没有user_id')
                return False
            user_entity: User = await self.client.get_entity(msg_entity.fwd_from.from_id.user_id)
            self.logger.info(f'获取目标用户实体成功:{user_entity}')
            if isinstance(user_entity, User):
                return user_entity
        self.logger.warning(f'获取目标用户实体失败: group_msg_id:{group_msg_id} 该实体不是用户实体')
        return False

    async def get_forward_vcard_entity(self, group_msg_id):
        msg_entity: Message = await self.client.get_messages(self.target_group_entity, ids=group_msg_id)
        print(msg_entity)
        if msg_entity:
            if msg_entity.media.user_id == 0:
                self.logger.warning(f'获取目标用户实体失败: group_msg_id:{group_msg_id} 该实体无效')
                return False
            user_entity: User = await self.client.get_entity(msg_entity.media.user_id)
            self.target_phone = msg_entity.media.phone_number
            self.logger.info(f'获取目标用户实体成功:{user_entity}')
            if isinstance(user_entity, User):
                return user_entity
        self.logger.warning(f'获取目标用户实体失败: group_msg_id:{group_msg_id} 该实体不是用户实体')
        return False

    async def get_group_entity(self, group_link: str, group_title: str):
        """获取群实体"""
        if await self.join_group(group_link):
            group_entity = await self.get_entity_by_dialogs(group_title)
            if group_entity:
                return group_entity
            else:
                return await self.client.get_entity(group_link)
        return None

    def parse_pull_group_status(self, data: dict):
        """
        解析拉群状态
        :param data:
        :return:
        """

        # group_info:dict -------------------------------------

        pull_status = data.get('pull_status')
        is_pull = 1
        pull_time = datetime.datetime.now()
        task_id = data.get('task_id')
        target_user_id = data.get('target_user_id')
        local_group_id = data.get('local_group_id')
        local_group_username = data.get('local_group_username')
        local_group_title = data.get('local_group_title')
        pull_type = data.get('pull_type')
        pull_group_username = data.get('pull_group_username')
        pull_group_id = data.get('pull_group_id')

        pull_group_info = {
            'is_pull': is_pull,
            'target_user_id': target_user_id,
            'pull_status': pull_status,
            'target_user_pull_time': pull_time,
            'update_time': pull_time,
            'pull_group_username': pull_group_username,
            'pull_group_id': pull_group_id,
            'local_group_username': local_group_username,
            'local_group_id': local_group_id,
            'local_group_title': local_group_title,
            'pull_type': pull_type,
            'work_phone': self.par.work_phone,
            'target_phone': self.target_phone
        }

        if pull_status == 'error':
            wait_time_error = 1296000  # 秒 15天
            self.redis_fun.pull_group_set_work_update_account_is_use(self.par.work_phone, wait_time_error)

        else:
            wait_time_normal = 46400  # 秒 1天
            if pull_status == 'miss':
                permissions_invite = data.get('permissions_invite')
                permissions_msg = data.get('permissions_msg')
                self.redis_fun.pull_group_set_group_and_user_is_pull(task_id, target_user_id, 'pull_miss')
                mysql_write_push('forward_user_update_user_premium_permissions',
                                 {
                                     'user_id': target_user_id,
                                     'permissions_invite': permissions_invite,
                                     'permissions_msg': permissions_msg,
                                     'mutual_contact': None,
                                     'too_many_pull': None
                                 })
                self.redis_fun.pull_group_set_work_update_account_is_use(self.par.work_phone, wait_time_normal)
            elif pull_status == 'success':
                self.redis_fun.pull_group_set_group_and_user_is_pull(task_id, target_user_id, 'success')
                self.redis_fun.pull_group_set_work_update_account_is_use(self.par.work_phone, wait_time_normal)
            elif pull_status == 'pull_already':
                self.redis_fun.pull_group_set_group_and_user_is_pull(task_id, target_user_id, 'pull_already')
                self.redis_fun.pull_group_set_work_update_account_is_use(self.par.work_phone, wait_time_normal)
            elif pull_status == 'mutual_contact':
                self.redis_fun.pull_group_set_group_and_user_is_pull(task_id, target_user_id, 'mutual_contact')
                self.redis_fun.pull_group_set_work_update_account_is_use(self.par.work_phone, wait_time_normal)
                mysql_write_push('forward_user_update_user_premium_permissions',
                                 {
                                     'user_id': target_user_id,
                                     'permissions_invite': None,
                                     'permissions_msg': None,
                                     'mutual_contact': 1,
                                     'too_many_pull': None
                                 })
            elif pull_status == 'too_many_requests' or 'too_many_requests_1':
                self.redis_fun.pull_group_update_target_user_id_can_pull(self.par.work_phone, target_user_id)
                pull_count = self.redis_fun.pull_group_get_target_user_id_can_pull(
                    self.par.work_phone, target_user_id)
                self.logger.info(f'当前账号被邀请次数 {pull_count}  target_user_id {target_user_id}')
                if len(pull_count) >= 2:
                    mysql_write_push('forward_user_update_user_premium_permissions',
                                     {
                                         'user_id': target_user_id,
                                         'permissions_invite': None,
                                         'permissions_msg': None,
                                         'mutual_contact': None,
                                         'too_many_pull': 1
                                     })
                    for work_phone in pull_count:
                        work_phone = work_phone.replace(f'pull_user_too_many:{target_user_id}', '')
                        self.redis_fun.pull_group_set_work_update_account_is_use(work_phone, 6000)
                else:
                    pull_group_info['is_pull'] = 0
            print('---------')
            print(pull_group_info)
            print('---------')
            # mysql_write_push('pull_group_update_pull_status', pull_group_info)
            self.db.pull_group_update_pull_status(pull_group_info)

        if 'error' in data:
            error = data.get('error')
        else:
            error = None

        work_account_pull_group_insert_info = {
            'work_phone': self.par.work_phone,
            'pull_status': pull_status,
            'error': error,
            'pull_time': pull_time,
        }
        mysql_write_push('work_account_pull_group_insert_info', work_account_pull_group_insert_info)

    async def pull_group(self, local_group_entity: Channel | Chat, user_entity: User,
                         pull_info: dict) -> InvitedUsers | bool | str:
        """
        拉群
        :param local_group_entity: 本地群实体
        :param user_entity: 目标用户实体
        :param pull_info: 拉群信息
        :return:
        """
        self.logger.info(f'{self.par} - 开始执行拉群行为 {user_entity}')
        user_input_entity: InputUser = InputUser(user_entity.id, user_entity.access_hash)
        try:
            if isinstance(local_group_entity, Channel):
                local_group_input_entity: InputChannel = InputChannel(local_group_entity.id,
                                                                      local_group_entity.access_hash)
                res: InvitedUsers = await self.client(
                    InviteToChannelRequest(local_group_input_entity, [user_input_entity]))
                self.logger.info(f'work_phone:{self.par.work_phone} | InvitedUsers: {res}')
            else:
                self.logger.warning(f'work_phone:{self.par.work_phone}==='
                                    f'请求失败! 未知群组类型 {local_group_entity}')
                return False

            if res.missing_invitees:
                pull_info['permissions_invite'] = res.missing_invitees[0].premium_would_allow_invite
                pull_info['permissions_msg'] = res.missing_invitees[0].premium_required_for_pm
                pull_info['pull_status'] = 'miss'

                self.logger.info(f'work_phone:{self.par.work_phone}==='
                                 f'请求成功! 拉群成功 !!!!!!!!  {user_entity.first_name} {user_entity.last_name}  |'
                                 f'  {user_entity.username} | {user_entity.id} | pull miss!')

                self.parse_pull_group_status(pull_info)

                return True
            if res.updates.users:
                pull_info['pull_status'] = 'success'

                self.logger.info(f'work_phone:{self.par.work_phone}==='
                                 f'请求成功! 拉群成功 !!!!!!!!    {user_entity.first_name} {user_entity.last_name}  |  {user_entity.username} | {user_entity.id}|pull_success')

                self.parse_pull_group_status(pull_info)

                return True
            else:
                pull_info['pull_status'] = 'pull_already'

                self.logger.info(f'work_phone:{self.par.work_phone}==='
                                 f'请求成功! 请检查群成员列表是否打开,未获取到返回信息    {user_entity.first_name} {user_entity.last_name}  |  {user_entity.username} {user_entity.id}|pull_already')

                self.parse_pull_group_status(pull_info)

                return True
        # 拉群账号被限制
        except telethon.errors.rpcerrorlist.UserBannedInChannelError as e:
            self.logger.error(f"work_phone:{self.par.work_phone}===="
                              f"请求失败! 群组拉人受限 UserBannedInChannelError {e}")
            pull_info['pull_status'] = 'error'
            pull_info['error'] = str(e)

            self.parse_pull_group_status(pull_info)

            return False
        # 被拉群账号被邀请次数过多,重试2次后放弃该账号
        except telethon.errors.rpcerrorlist.PeerFloodError as e:
            self.logger.error(f"work_phone:{self.par.work_phone}===="
                              f"请求失败! 请求超限 PeerFloodError {e}")

            pull_info['pull_status'] = 'too_many_requests'
            pull_info['error'] = str(e)

            self.parse_pull_group_status(pull_info)
            return False
        # 洪水
        except telethon.errors.rpcerrorlist.FloodWaitError as e:
            self.logger.error(f"请求失败! 请求超限 PeerFloodError {e}")
            pull_info['pull_status'] = 'error'
            pull_info['error'] = str(e)

            self.parse_pull_group_status(pull_info)
            return False
        # 拉群账号限制 或者 被拉群账号限制联系人邀请权限
        except telethon.errors.rpcbaseerrors.BadRequestError as e:
            self.logger.error(f"work_phone:{self.par.work_phone}===="
                              f"请求失败! 请求错误 BadRequestError {e}")
            pull_info['pull_status'] = 'too_many_requests_1'
            pull_info['error'] = str(e)

            self.parse_pull_group_status(pull_info)
            return False
        # 暂时未知
        except ValueError as e:
            self.logger.error(f"work_phone:{self.par.work_phone}===="
                              f"请求失败! 请求错误 ValueError {e}")

            pull_info['pull_status'] = 'error'
            pull_info['error'] = str(e)

            self.parse_pull_group_status(pull_info)
            return False
        # 没有权限邀请用户进群
        except telethon.errors.rpcerrorlist.ChatWriteForbiddenError as e:
            self.logger.error(f"work_phone:{self.par.work_phone}===="
                              f"请求失败! 没有权限写入群组 ChatWriteForbiddenError {e}")

            pull_info['pull_status'] = 'error'
            pull_info['error'] = str(e)

            self.parse_pull_group_status(pull_info)
            return False

    async def add_user_to_contact(self, user_entity: User):
        input_user_entity = InputUser(user_entity.id, user_entity.access_hash)
        if user_entity.first_name is None and user_entity.last_name is None:
            first_name = 'name_'.join(random.choices(string.ascii_letters, k=random.randint(2, 4)))
            last_name = ''
        elif user_entity.first_name is None:
            first_name = ''
            last_name = user_entity.last_name
        else:
            first_name = user_entity.first_name
            last_name = ''

        self.logger.info(f'work_phone:{self.par.work_phone}==='
                         f'添加联系人昵称设置: {first_name} {last_name} {user_entity.id}')
        res: Updates = await self.client(functions.contacts.AddContactRequest(
            id=input_user_entity,
            first_name=first_name,
            last_name=last_name,
            phone=''.join(random.choices(string.digits, k=11))
        ))
        print(res)
        user_entity = res.users[0]
        return user_entity
        # contacts = await self.client(GetContactsRequest(0))
        # for contact in contacts.users:
        #     if contact.id == user_entity.id:
        #         return contact
        #     return None
        # return None

    async def start_pull_group(self, local_group_entity, task_id, pull_type, local_group_link):

        for i in range(20):
            target_user_info_list: list[PullGroup] = self.db.pull_group_get_target_users(task_id, 1)

            if not target_user_info_list:
                self.logger.warning(f'work_phone:{self.par.work_phone}==='
                                    f'没有目标用户')
                return 'NO_ACCOUNT'
            self.logger.info(f'获取到本次拉群目标 {len(target_user_info_list)} 个')
            target_user_info = target_user_info_list[0]
            try:
                # if target_user_info.pull_group_username != self.target_group_username or self.target_group_username is None:
                #     self.target_group_username = target_user_info.pull_group_username
                #     self.target_group_title = target_user_info.pull_group_title
                # self.target_group_entity = await self.get_group_entity(self.target_group_username,
                #                                                        self.target_group_title)
                # print('target_group_entity:', self.target_group_entity)
                # if not self.target_group_entity:
                #     self.logger.warning(f'work_phone:{self.par.work_phone}=== 实体群加群失败')
                #     return None

                if target_user_info.pull_type == 'forward_msg':
                    user_entity: User = await self.get_forward_user_entity(target_user_info.pull_group_msg_id)
                elif target_user_info.pull_type == 'forward_vcard':
                    user_entity: User = await self.get_forward_vcard_entity(target_user_info.pull_group_msg_id)


                else:
                    self.logger.warning(
                        f'work phone {self.par.work_phone} -- target_user_id:{target_user_info.target_user_id} 未知的拉群方式')
                    continue
                if not user_entity:
                    mysql_write_push('pull_group_update_is_pull',
                                     {'target_user_id': target_user_info.target_user_id, 'task_id': task_id,
                                      'is_pull': 1, 'pull_status': 'user_miss'})
                    mysql_write_push('pull_group_update_is_pull',
                                     {'user_id': target_user_info.target_user_id, 'save_status': 0})
                    self.logger.warning(
                        f'work_phone:{self.par.work_phone} -- target_user_id:{target_user_info.target_user_id} 获取实体失败')
                    continue
                if user_entity.deleted:
                    self.logger.warning(f'work_phone:{self.par.work_phone}==='
                                        f'请求失败! 该用户已被删除')
                    mysql_write_push('pull_group_update_is_pull',
                                     {'target_user_id': target_user_info.target_user_id, 'task_id': task_id,
                                      'is_pull': 1, 'pull_status': 'user_deleted'})
                    mysql_write_push('pull_group_update_is_pull', {'user_id': user_entity.id, 'save_status': 0})
                    continue
                user_was_online_time = get_user_was_online_time(user_entity)
                if datetime.datetime.now() - user_was_online_time > datetime.timedelta(days=self.activate_time_day):
                    self.logger.warning(f'work_phone:{self.par.work_phone}==='
                                        f'该用户在线时间不在 {self.activate_time_day} 天内上线，不符合条件')
                    mysql_write_push('pull_group_update_is_pull',
                                     {'target_user_id': target_user_info.target_user_id, 'task_id': task_id,
                                      'is_pull': 1, 'pull_status': 'online_time_out'})
                    mysql_write_push('pull_group_update_is_pull', {'user_id': user_entity.id, 'save_status': 0})
                    continue

                contact: User = await self.add_user_to_contact(user_entity=user_entity)

                pull_info = {
                    "task_id": task_id,
                    "pull_group_username": self.target_group_username,
                    "pull_group_id": self.target_group_entity.id,
                    "local_group_username": local_group_link,
                    "local_group_id": local_group_entity.id,
                    "local_group_title": local_group_entity.title,
                    "pull_type": pull_type,
                    "target_user_id": contact.id,
                    "work_phone": self.par.work_phone,
                    "target_phone": self.target_phone

                }

                pull_status = await self.pull_group(local_group_entity, contact, pull_info)

                if not pull_status:
                    mysql_write_push('pull_group_update_is_pull',
                                     {'target_user_id': target_user_info.target_user_id, 'task_id': task_id,
                                      'is_pull': 0, 'pull_status': None})
                return True
            except asyncio.exceptions.IncompleteReadError as e:
                self.logger.warning(f'work_phone:{self.par.work_phone}==='
                                    f'请求失败! 请求超时 {e}')
                mysql_write_push('pull_group_update_is_pull',
                                 {'target_user_id': target_user_info.target_user_id, 'task_id': task_id,
                                  'is_pull': 0, 'pull_status': None})
                return None
        return None

    async def start(self, local_group_link: str, local_group_title: str, task_id, pull_type, activate_time_day):
        self.target_group_entity = await self.get_users_entity_group()
        if not self.target_group_entity:
            return False
        self.activate_time_day = activate_time_day
        local_group_entity = await self.get_group_entity(local_group_link, local_group_title)
        if not local_group_entity:
            self.logger.info('获取本地群失败')
            return None
        await asyncio.sleep(1)
        target_get_group_status = await self.start_pull_group(local_group_entity, task_id, pull_type, local_group_link)
        self.logger.info('target_get_group_status:', target_get_group_status)
        if target_get_group_status == 'NO_ACCOUNT':
            return False
        return None
