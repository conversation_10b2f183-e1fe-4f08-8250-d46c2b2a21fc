import asyncio
import datetime
import logging
import random

from nb_log import get_logger
from telethon import TelegramClient
from telethon.tl.functions.contacts import SearchRequest
from telethon.tl.types import Channel
from telethon.tl.types.contacts import Found

from TG_account_manager_v2_1.base_action.create_client import Create<PERSON>lient
from conf.conf import base_set
from core.db.mysql_cn import WorkAccount, SearchChannelTask
from core.db.mysql_fun import MySqlAccountFun
from core.db.redis_fun import RedisFun


class SearchGroup:
    def __init__(self, parameter: WorkAccount, tg_client: TelegramClient, mysql_db: MySqlAccountFun,
                 redis_db: RedisFun, logger: logging.Logger):
        self.par = parameter
        self.client: TelegramClient = tg_client
        self.mysql_db = mysql_db
        self.redis_db = redis_db
        self.logger = logger

    def get_search_task(self):
        search_task: list[SearchChannelTask] = self.mysql_db.get_search_task()
        return search_task

    def check_and_update_channel_info(self, channel: Channel, label,search_keywords):
        if isinstance(channel, Channel):
            title = channel.title
            if channel.username:
                username = channel.username
            elif channel.usernames:
                username = channel.usernames[0].username
            else:
                username = None
            group_id = channel.id
            if channel.verified:
                """已认证"""
                verified = 1
            else:
                verified = 0
            if channel.broadcast:
                """true频道"""
                broadcast = 1
                group_type = 0
            else:
                group_type = 1
                broadcast = 0
            if channel.forum:
                """广播频道"""
                forum = 1
            else:
                forum = 0
            if channel.megagroup:
                """超级群主"""
                megagroup = 1
            else:
                megagroup = 0
            participants_count = channel.participants_count
            channel_info = channel.__str__()
            update_info = dict(
                keyword=search_keywords,
                title=title,
                username=username,
                group_id=group_id,
                group_type=group_type,
                verified=verified,
                broadcast=broadcast,
                forum=forum,
                megagroup=megagroup,
                participants_count=participants_count,
                channel_info=channel_info,
                label=label,
                update_time=datetime.datetime.now(),
                create_time=datetime.datetime.now()
            )
            self.mysql_db.search_res_update_group_info(update_info)
            self.logger.info(f'更新频道信息成功 {update_info}')

    async def search_channel(self, search_task: SearchChannelTask):
        result: Found = await self.client(SearchRequest(
            q=search_task.search_keywords,
            limit=30
        ))
        chats = result.chats
        for chat in chats:
            self.logger.info(f'开始搜索关键词: {search_task.search_keywords} - {chat.title}')
            self.check_and_update_channel_info(chat, search_task.label,search_task.search_keywords)

    async def start(self):
        search_task = self.get_search_task()
        if search_task:
            self.logger.info(f'work_phone: {self.par.work_phone}  proxy_port: {self.par.proxy_port} === '
                             f'当前开始执行搜索频道任务: {len(search_task)} 个')
            for task in search_task:
                self.logger.info(f'开始搜索关键词:{task.search_keywords}')
                await self.search_channel(task)
                self.mysql_db.update_search_task(task.id)
                await asyncio.sleep(random.randint(1, 3))

        else:
            self.logger.info(f'work_phone: {self.par.work_phone}  proxy_port: {self.par.proxy_port} === '
                             f'没有可搜索的频道！')
            return False

if __name__ == '__main__':
    db = MySqlAccountFun()
    redis_fun = RedisFun()
    logger_init = get_logger(name=f'{datetime.date.today()}',
                             log_path=f'{base_set.log_dir}',
                             log_filename=f'{datetime.date.today()}.log')

    test_account: dict = db.get_work_accounts_by_account_type('search_group')
    for account in test_account:


        async def main():
            client: TelegramClient = await CreateClient(account, db, logger_init).start_get_client()
            sg = SearchGroup(account, client, db, redis_fun, logger_init)
            await sg.start()

        asyncio.run(main())

