import asyncio
import datetime
import logging
from nb_log import get_logger

import telethon
from telethon import TelegramClient
from telethon.errors import rpc_errors_dict
from telethon.tl.functions.contacts import GetContactsRequest
from telethon.tl.functions.messages import SendMediaRequest
from telethon.tl.types import User, InputMediaContact, Updates
from telethon.tl.patched import Message

from TG_account_manager_v2_1.base_action.base_action import BaseAction
from TG_account_manager_v2_1.base_action.create_client import CreateClient
from conf.conf import base_set
from core.db.mysql_cn import DBSession, WorkAccount
from core.db.mysql_fun import MySqlAccountFun
from core.db.redis_fun import RedisFun
from core.models.work_account_models import AccountModel
from run.boost_mysql import mysql_write_push


class RaiseForwardMsg(BaseAction):
    def __init__(self, parameter: WorkAccount, tg_client: TelegramClient, mysql_db: MySqlAccountFun,
                 redis_db: RedisFun, logger: logging.Logger):
        super().__init__(parameter, tg_client, logger, mysql_db, redis_db)
        self.par = parameter
        self.client: TelegramClient = tg_client
        self.mysql_db = mysql_db
        self.redis_db = redis_db
        self.logger = logger

        # self.forward_group_link = 'https://t.me/+psrCMx-19fg1NWE1'
        # self.forward_group_title = 'whzl_users'
        # self.label = 'whzl_gp_3'
        # self.collection_group_username = 'BinanceChinese'

        # self.forward_group_link = 'https://t.me/+WU5MbAwV8hJlY2Q0'
        # self.forward_group_title = 'account_2'
        # self.label = 'web3'
        # self.collection_group_username = 'BinanceChinese'

        self.forward_group_link = 'https://t.me/+psrCMx-19fg1NWE1'
        self.forward_group_title = 'whzl_users'
        self.label = 'whzl_vantage_p'

    async def start(self):
        """开始转发消息"""

        forward_group = await self.join_group(self.forward_group_link)
        if not forward_group:
            self.logger.info(f'加入群组失败')
            return

        forward_group_entity = await self.get_entity_by_dialogs(self.forward_group_title)
        if not forward_group_entity:
            self.logger.info(f'获取群组失败')
            return

        while True:
            collection_msg_info = self.mysql_db.collection_user_get_forward_msg(self.label)
            # collection_msg_info = self.mysql_db.collection_user_get_forward_msg_by_group_username(
            #     self.collection_group_username)
            if not collection_msg_info:
                self.logger.info(f'没有需要转发的消息')
                await asyncio.sleep(60)
                return

            group_username = collection_msg_info.collection_group_username
            group_msg_id = collection_msg_info.collection_msg_id
            user_id = collection_msg_info.user_id
            if group_username == 'hejzl' or group_username is None:
                continue
            if 'joinchat' in group_username or '+' in group_username:
                try:
                    join_status = await self.join_group(group_username)
                    if not join_status:
                        self.logger.info(f'join error {group_username}')
                    group_entity = await self.client.get_entity(group_username)

                    msg: Message = await self.client.get_messages(group_entity, ids=group_msg_id)
                    self.logger.info(f'{group_entity} ------ {group_msg_id}')
                    print(msg)
                    try:
                        user_entity = await msg.get_sender()
                    except Exception as e:
                        self.logger.warning(f'{group_msg_id} --- 消息不存在,或用户不可用')

                        update_forward_info = {
                            'user_id': user_id,
                            'forward_group_id': 0000000,
                        }
                        print(update_forward_info)
                        mysql_write_push('collection_user_update_forward_status', update_forward_info)
                        return False

                    fw_msg: Message = await self.client.forward_messages(forward_group_entity, msg)

                    forward_msg_id = fw_msg.id

                    update_forward_info = {
                        'user_id': user_entity.id,
                        'username': user_entity.username,
                        'first_name': user_entity.first_name,
                        'last_name': user_entity.last_name,
                        'save_status': 1,
                        'forward_type': 1,
                        'forward_group_title': self.forward_group_title,
                        'forward_group_link': self.forward_group_link,
                        'forward_msg_id': forward_msg_id,
                        'label': self.label,
                        'source': 'collection',
                        'update_time': datetime.datetime.now(),
                        'create_time': datetime.datetime.now(),
                    }
                    print(update_forward_info)
                    mysql_write_push('forward_insert_vcard_user_info', update_forward_info)
                    # mysql_write_push('collection_user_update_forward_user_info', update_forward_info)

                    update_collection_info = {
                        'user_id': user_entity.id,
                        'forward_group_id': forward_group_entity.id,
                        'forward_group_username': self.forward_group_link,
                        'forward_msg_id': forward_msg_id,
                    }
                    mysql_write_push('collection_user_update_forward_user', update_collection_info)

                    self.logger.info(f'转发消息成功 - {user_entity.id} - {forward_msg_id}')
                    # await asyncio.sleep(5)
                except ValueError as e:
                    continue
            else:
                try:
                    group_entity = await self.client.get_entity(group_username)

                    msg: Message = await self.client.get_messages(group_entity, ids=group_msg_id)
                    self.logger.info(f'{group_entity} ------ {group_msg_id}')
                    print(msg)
                    try:
                        user_entity = await msg.get_sender()
                    except Exception as e:
                        self.logger.warning(f'{group_msg_id} --- 消息不存在,或用户不可用')

                        update_forward_info = {
                            'user_id': user_id,
                            'forward_group_id': 0000000,
                        }
                        print(update_forward_info)
                        mysql_write_push('collection_user_update_forward_status', update_forward_info)
                        return False

                    fw_msg: Message = await self.client.forward_messages(forward_group_entity, msg)

                    forward_msg_id = fw_msg.id

                    update_forward_info = {
                        'user_id': user_entity.id,
                        'username': user_entity.username,
                        'first_name': user_entity.first_name,
                        'last_name': user_entity.last_name,
                        'save_status': 1,
                        'forward_type': 1,
                        'forward_group_title': self.forward_group_title,
                        'forward_group_link': self.forward_group_link,
                        'forward_msg_id': forward_msg_id,
                        'label': self.label,
                        'source': 'collection',
                        'update_time': datetime.datetime.now(),
                        'create_time': datetime.datetime.now(),
                    }
                    print(update_forward_info)
                    mysql_write_push('forward_insert_vcard_user_info', update_forward_info)
                    # mysql_write_push('collection_user_update_forward_user_info', update_forward_info)

                    update_collection_info = {
                        'user_id': user_entity.id,
                        'forward_group_id': forward_group_entity.id,
                        'forward_group_username': self.forward_group_link,
                        'forward_msg_id': forward_msg_id,
                    }
                    mysql_write_push('collection_user_update_forward_user', update_collection_info)

                    self.logger.info(f'转发消息成功 - {user_entity.id} - {forward_msg_id}')
                except ValueError as e:
                    continue
            await asyncio.sleep(0.2)


if __name__ == '__main__':
    Session = DBSession()
    sql_db = MySqlAccountFun()
    redis_fun = RedisFun()

    logger_init = get_logger(name=f'{datetime.date.today()}',
                             log_path=f'{base_set.log_dir}',
                             log_filename=f'{datetime.date.today()}.log')


    def get_can_use_work_account(offset):
        """获取可用账号"""
        with Session.Session() as session:
            work_accounts = session.query(
                WorkAccount
            ).filter(
                WorkAccount.save_status == 1,
                WorkAccount.use_status == 0,
            ).offset(offset).all()
            if work_accounts:
                return [account.__dict__ for account in work_accounts]
            return None


    async def main():
        i = 400
        while True:
            try:
                print(i)
                accounts = get_can_use_work_account(i)
                print(len(accounts))
                for account in accounts:
                    account.pop('_sa_instance_state')
                    mysql_write_push('work_account_update_use_status',
                                     {'work_phone': account['work_phone'], 'use_status': 1})
                    par = WorkAccount(**account)
                    client: TelegramClient = await CreateClient(par, sql_db, logger_init).start_get_client()
                    if client:
                        try:
                            print(f'start - {par.work_phone}')
                            rasc = RaiseForwardMsg(parameter=par, mysql_db=sql_db, redis_db=redis_fun,
                                                   logger=logger_init,
                                                   tg_client=client)
                            await rasc.start()
                            await client.disconnect()
                            # break
                        # except Exception as e:
                        #     print(e)

                        finally:
                            mysql_write_push('work_account_update_use_status',
                                             {'work_phone': account['work_phone'], 'use_status': 0})
            except telethon.errors.rpcerrorlist.FloodWaitError as e:
                i = i + 1
                continue
            except asyncio.exceptions.IncompleteReadError as e:
                i = i + 1
                continue


    asyncio.run(main())
