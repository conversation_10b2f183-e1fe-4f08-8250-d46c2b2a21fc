import datetime
import logging

import telethon
from telethon import Telegram<PERSON>lient
from telethon.errors import rpc_errors_dict
from telethon.tl.functions.contacts import GetContactsRequest
from telethon.tl.functions.messages import SendMediaRequest
from telethon.tl.types import User, InputMediaContact, Updates

from TG_account_manager_v2_1.base_action.base_action import BaseAction
from core.db.mysql_cn import Contact
from core.db.mysql_fun import MySqlAccountFun
from core.db.redis_fun import RedisFun
from core.models.work_account_models import AccountModel
from run.boost_mysql import mysql_write_push


class RaiseUpdateContactOlineTime(BaseAction):
    def __init__(self, parameter: AccountModel, tg_client: TelegramClient, mysql_db: MySqlAccountFun,
                 redis_db: RedisFun, logger: logging.Logger):
        super().__init__(parameter, tg_client, logger, mysql_db, redis_db)
        self.par = parameter
        self.client: TelegramClient = tg_client
        self.mysql_db = mysql_db
        self.redis_db = redis_db
        self.logger = logger

    async def start(self):
        contacts = await self.client
