import asyncio
import concurrent.futures
import datetime
import sys
from concurrent.futures import <PERSON>hr<PERSON><PERSON><PERSON><PERSON>xecutor

from nb_log import get_logger
from telethon import TelegramClient

from TG_account_manager_v2_1.action.pull_group.pull_group_v1 import GetTargetUserIntoGrop
from TG_account_manager_v2_1.base_action.create_client import CreateClient
from conf.conf import base_set
from core.db.mysql_cn import WorkAccount, DBSession, PullGroup, CollectionUser
from core.db.mysql_fun import MySqlAccountFun
from core.db.redis_fun import RedisFun
from run.boost_mysql import mysql_write_push
from sqlalchemy import func, or_

Session = DBSession()
sql_db = MySqlAccountFun()
redis_fun = RedisFun()

logger_init = get_logger(name=f'{datetime.date.today()}',
                         log_path=f'{base_set.log_dir}',
                         log_filename=f'{datetime.date.today()}.log')


class ProgramControl:
    should_stop = False


program_control = ProgramControl()


def get_new_task_id():
    with Session.Session() as session:
        # 查询 pull_group 表中 task_id 的最大值
        max_task_id = session.query(func.max(PullGroup.task_id)).scalar()

        # 如果表中没有数据，max_task_id 将会是 None
        if max_task_id is None:
            return 1
        else:
            # 返回最大值加1
            return max_task_id + 1


def create_pull_task_data(new_task_id, task_count, label, project_name, project_subcategory):
    with Session.Session() as session:
        task_data: list[CollectionUser] = session.query(CollectionUser).filter(CollectionUser.label == label).limit(
            task_count).all()
        if task_data:
            for data in task_data:
                session.add(PullGroup(
                    project_name=project_name,
                    project_subcategory=project_subcategory,
                    target_username=data.username,
                    target_user_id=data.user_id,
                    task_id=new_task_id,
                    pull_group_username=data.collection_group_username,
                    pull_group_msg_id=data.collection_msg_id
                ))
            session.commit()


def create_pull_task(label, task_count, project_name, project_subcategory):
    """创建拉群任务"""
    new_task_id = get_new_task_id()
    create_pull_task_data(new_task_id, task_count, label, project_name, project_subcategory)
    print(f'创建拉群任务成功, task_id: {new_task_id}')
    return new_task_id


def get_can_use_work_account(account_count, offset_num):
    """获取可用账号"""
    with Session.Session() as session:
        work_accounts = session.query(
            WorkAccount
        ).filter(
            WorkAccount.save_status == 1,
            WorkAccount.use_status == 0,
            or_(
                WorkAccount.account_type != 'update_online_time',
                WorkAccount.account_type == None  # 处理 NULL 值
            )
        ).limit(account_count).offset(offset_num).all()
        if work_accounts:
            return [account.__dict__ for account in work_accounts]
        return None


async def process_single_account(account, pull_info, sql_db, redis_fun, logger_init):
    """处理单个账号的任务"""
    if program_control.should_stop:
        return

    if '_sa_instance_state' in account:
        account.pop('_sa_instance_state')

    if redis_fun.pull_group_work_account_is_use(account['work_phone']):
        print(f'{account["work_phone"]} 已经在使用中')
        return

    try:
        mysql_write_push('work_account_update_use_status', {'work_phone': account['work_phone'], 'use_status': 1})
        par = WorkAccount(**account)
        print(par)

        client: TelegramClient = await CreateClient(par, sql_db, logger_init).start_get_client()
        if client:
            try:
                print(f'start - {par.work_phone}')
                init_ac = GetTargetUserIntoGrop(par, sql_db, redis_fun, logger_init, client)
                res = await init_ac.start(
                    pull_info['local_group_username'],
                    pull_info['project_subcategory'],
                    pull_info['task_id'],
                    pull_info['pull_type'],
                    pull_info['activate_time_day'],
                )

                # 检查返回结果
                if res is False:
                    print(f"账号 {par.work_phone} 执行失败，停止所有任务")
                    program_control.should_stop = True
                    sys.exit(1)
            finally:
                # 确保客户端正确断开连接
                await client.disconnect()
    finally:
        # 无论如何都更新账号状态
        mysql_write_push('work_account_update_use_status', {'work_phone': account['work_phone'], 'use_status': 0})


def process_single_account_wrapper(account, pull_info, sql_db, redis_fun, logger_init):
    """包装异步函数，使其可以在线程池中运行"""
    new_loop = asyncio.new_event_loop()
    asyncio.set_event_loop(new_loop)
    try:
        return new_loop.run_until_complete(process_single_account(account, pull_info, sql_db, redis_fun, logger_init))
    except Exception as e:
        print(f"处理账号 {account['work_phone']} 时发生错误: {e}")
        return None
    finally:
        # 确保在关闭循环前所有任务都已完成
        pending = asyncio.all_tasks(new_loop)
        if pending:
            new_loop.run_until_complete(asyncio.gather(*pending, return_exceptions=True))
        new_loop.close()


async def main(pull_info):
    account_count = pull_info['task_pull_count']
    offset_num = pull_info['offset_num']
    accounts = get_can_use_work_account(account_count, offset_num)

    if not accounts:
        print('没有可用账号')
        return

    print(f'总共获取到 {len(accounts)} 个账号')

    # 创建线程池，最大线程数设为10
    max_workers = 15
    loop = asyncio.get_event_loop()

    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        try:
            # 创建任务列表
            futures = []
            for account in accounts:
                if program_control.should_stop:
                    break

                # 将每个账号的处理提交到线程池，不使用asyncio.run()
                future = loop.run_in_executor(
                    executor,
                    process_single_account_wrapper,
                    account, pull_info, sql_db, redis_fun, logger_init
                )
                futures.append(future)

            # 等待所有任务完成
            await asyncio.gather(*futures, return_exceptions=True)

        except SystemExit:
            print("程序收到停止信号，正在清理资源...")
            # 取消所有未完成的任务
            for future in futures:
                if not future.done():
                    future.cancel()
            raise
        except Exception as e:
            print(f"发生未预期的错误: {e}")
            raise


if __name__ == '__main__':

    # pull_info_dict = {
    #     'project_name': 'sport_yf_3',
    #
    #     # 'local_group_username': 'https://t.me/+7XoRM_khlMViNWE0',
    #     # 'project_subcategory': 'A1❤️BẢO TRÂM',
    #
    #     # 'local_group_username': 'https://t.me/+Blh2AxfRqxtlZmM0',
    #     # 'project_subcategory': 'A2♥️ THU THẢO BCR ❤️',
    #
    #     # 'local_group_username': 'https://t.me/+yIrSAZWCckJmMjhk',
    #     # 'project_subcategory': 'A3♥️88lucky giao lưu chia sẻ kinh nghiệm BCR',
    #
    #     # 'local_group_username': 'https://t.me/+SxWGcpzTKfNkNWZk',
    #     # 'project_subcategory': 'A4♥️ PHƯƠNG THẢO ❤️',
    #
    #     # 'local_group_username': 'https://t.me/+FnRb_uU51-pmZWQ0',
    #     # 'project_subcategory': 'A5♥️ QUỲNH ANH BCR ♥️',
    #
    #     # 'local_group_username': 'https://t.me/+FBExp_KbaZFiZmE0',
    #     # 'project_subcategory': 'A6 ♥️ BẢO LINH BCR ♥️LUCKY 88',
    #
    #     # 'local_group_username': 'https://t.me/+-2J6iCjhTQxlZDRi',
    #     # 'project_subcategory': 'A31❤️88lucky',
    #     #
    #     # 'local_group_username': 'https://t.me/+PLXDW4M-vVRkY2Y6',
    #     # 'project_subcategory': 'A32❤️88lucky',
    #
    #     # 'local_group_username': 'https://t.me/+rRjhi_Ujfd4wMTZi',
    #     # 'project_subcategory': 'A33❤️88lucky',
    #
    #     'local_group_username': 'https://t.me/+Ce7yt0fA920wMGZi',
    #     'project_subcategory': 'A34❤️88lucky',
    #     #
    #     # 'local_group_username': 'https://t.me/+hl2-tJIhm6xkM2My',
    #     # 'project_subcategory': 'A35❤️88lucky',
    #
    #     # 'local_group_username': 'https://t.me/+g7n8S2slH2IyNTcy',
    #     # 'project_subcategory': 'A36❤️88lucky',
    #
    #     # 'local_group_username': 'https://t.me/+DPZVydmJjJA3MWNi',
    #     # 'project_subcategory': 'A37❤️88lucky',
    #     #
    #     # 'local_group_username': 'https://t.me/+PvhT3VPorl0yYWIy',
    #     # 'project_subcategory': 'A38❤️88lucky',
    #     #
    #     # 'local_group_username': 'https://t.me/+xo2W3M08Wtc4ZDJi',
    #     # 'project_subcategory': 'A39❤️88lucky',
    #     #
    #     # 'local_group_username': 'https://t.me/+RkwwP9r-mHwwOGMy',
    #     # 'project_subcategory': 'A40❤️88lucky',
    #
    #     'task_id': 35,
    #     'task_pull_count': 600,
    #     'offset_num': 9500,
    #     'pull_type': 'forward_vcard',
    #     'activate_time_day': 30
    # }

    # pull_info_dict = {
    #     'project_name': 'sport_yf_3',
    #
    #
    #
    #
    #     # 'local_group_username': 'https://t.me/+NK8eVW7eT8o4Njcy',
    #     # 'project_subcategory': 'A41❤️88lucky',
    #
    #     # 'local_group_username': 'https://t.me/+ks5TJakCSpo2Y2Ey',
    #     # 'project_subcategory': 'A42❤️88lucky',
    #
    #     # 'local_group_username': 'https://t.me/+OibdeOmB9BljMjUy',
    #     # 'project_subcategory': 'A43❤️88lucky',
    #
    #     # 'local_group_username': 'https://t.me/+f69e3o0nGLY5ZDc6',
    #     # 'project_subcategory': 'A44❤️88lucky',
    #
    #     # 'local_group_username': 'https://t.me/+ZLZrJsm7UJI3OWJi',
    #     # 'project_subcategory': 'A45❤️88lucky',
    #
    #     # 'local_group_username': 'https://t.me/+-J75_iJ9gZY3MTg6',
    #     # 'project_subcategory': 'A47❤️88lucky',
    #     #
    #     # 'local_group_username': 'https://t.me/+FgOEwwdkQvs1OWVi',
    #     # 'project_subcategory': 'A48❤️88lucky',
    #     #
    #     # 'local_group_username': 'https://t.me/+YownpsfOX6U0N2Yy',
    #     # 'project_subcategory': 'A49❤️88lucky',
    #     #
    #     # 'local_group_username': 'https://t.me/+uxzYzAsMyu0zNjM6',
    #     # 'project_subcategory': 'A50❤️88lucky',
    #
    #     # 'local_group_username': 'https://t.me/+XZMzGrTc_Wk0MDRi',
    #     # 'project_subcategory': 'A51❤️88lucky',
    #     #
    #     'local_group_username': 'https://t.me/+xvLlAvzakXI0MDAy',
    #     'project_subcategory': 'A52❤️88lucky',
    #     #
    #     # 'local_group_username': 'https://t.me/+rcD9PZUWkmZhYzYy',
    #     # 'project_subcategory': 'A53❤️88lucky',
    #     #
    #     # 'local_group_username': 'https://t.me/+9IcLM3KSZvQ5OTZi',
    #     # 'project_subcategory': 'A54❤️88lucky',
    #     #
    #     # 'local_group_username': 'https://t.me/+AVP4-Fl2FMs4NWIy',
    #     # 'project_subcategory': 'A55❤️88lucky',
    #
    #     # 'local_group_username': 'https://t.me/+36n9Ag2iuCVjNmYy',
    #     # 'project_subcategory': 'A56❤️88lucky',
    #
    #
    #
    #     'task_id': 32,
    #     'task_pull_count': 400,
    #     'offset_num': 10000,
    #     'pull_type': 'forward_vcard',
    #     'activate_time_day': 30
    # }

    # pull_info_dict = {
    #     'project_name': 'whzl_vantage',
    #
    #
    #     'local_group_username': 'https://t.me/+i6-JFadYcdc1Mzc8',
    #     'project_subcategory': 'Masters Traders Spanish',
    #
    #
    #     'task_id': 33,
    #     'task_pull_count': 600,
    #     'offset_num': 3000,
    #     'pull_type': 'forward_msg',
    #     'activate_time_day': 60
    # }

    pull_info_dict = {
        'project_name': 'whzl_vantage_p',


        'local_group_username': 'https://t.me/+lQAtQoY6fpw3ZGZk',
        'project_subcategory': 'Forex Dream Portuguese',


        'task_id': 36,
        'task_pull_count': 200,
        'offset_num': 4800,
        'pull_type': 'forward_msg',
        'activate_time_day': 60
    }

    try:

        asyncio.run(main(pull_info_dict))
    except SystemExit:
        print("程序已停止执行")
    except Exception as e:
        print(f"程序发生错误: {e}")
    finally:
        print("程序清理完成")
